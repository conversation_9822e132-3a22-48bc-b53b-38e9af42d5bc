"""Project Utilities Module

This module provides functionality for project-related operations, including:
- Content extraction from processed documents
- Relevance checking for documents
- Agent execution for different project sections
- Model management

Key Features:
- Document content extraction
- Relevance determination
- Agent execution
- Model information
"""

import logging
import asyncio
from concurrent.futures import ProcessPoolExecutor
from crew import *
from utils.constants import DocumentTags, Sections

def get_combined_content_from_processed_doc(processed_doc):
    logging.info(f"Combining the whole content of file {processed_doc.file_name} having {len(processed_doc.sections)} pages")
    combined_content = "Document Name: " + processed_doc.file_name
    # combined_content = ""
    for page in processed_doc.sections:
        # combined_content+= f"""\n Page No. {page['page_number']} Hi \n"""

        combined_content+= f"""
        \n Page No. {page['page_number']}\n
        {page['content']}\n
    """

    return combined_content

def is_relevant_document(section_to_extract, assigned_tags):
        logging.info(f"Checking if this is a relevant document for section: {section_to_extract}")
        if assigned_tags == []:
            return True

        for tag in assigned_tags:
            if tag == DocumentTags.MAIN_DOCUMENT or tag == section_to_extract:
                return True

        return False

def run_relevant_agent_sync(section, purpose, content, user_guidelines=""):
    """Synchronous version of run_relevant_agent."""
    if purpose == "Extraction":
        agent_mapping = {
            Sections.PROPOSAL_INFO: run_proposal_info_extraction_agent,
            Sections.BACKGROUND: run_background_extraction_agent,
            Sections.SCOPE: run_scope_extraction_agent,
            Sections.TASK_AREA: run_task_areas_extraction_agent,
            Sections.SUBMISSION_INSTRUCTIONS: run_submission_instructions_extraction_agent,
            Sections.EVALUATION_FACTORS: run_evaluation_factors_extraction_agent,
            Sections.LIST_OF_ATTACHMENTS: run_list_of_attachments_extraction_agent,
            Sections.KEY_PERSONNEL: run_key_personnel_extraction_agent,
        }
    else:
        agent_mapping = {
            Sections.PROPOSAL_INFO: run_proposal_info_drafting_agent,
            Sections.BACKGROUND: run_background_drafting_agent,
            Sections.SCOPE: run_scope_drafting_agent,
            Sections.TASK_AREA: run_task_areas_drafting_agent,
            Sections.SUBMISSION_INSTRUCTIONS: run_submission_instructions_drafting_agent,
            Sections.EVALUATION_FACTORS: run_evaluation_factors_drafting_agent,
            Sections.LIST_OF_ATTACHMENTS: run_list_of_attachments_drafting_agent,
            Sections.KEY_PERSONNEL: run_key_personnel_drafting_agent,
        }

    if section in agent_mapping:
        agent_function = agent_mapping[section]
        return agent_function(content, user_guidelines)
    else:
        logging.info(f"No agent found for section: {section}")
        return ""

async def run_relevant_agent(section, purpose, content, user_guidelines=""):
    """Asynchronous version that uses ProcessPoolExecutor to run the sync version."""
    executor = ProcessPoolExecutor(max_workers=1)
    try:
        result, token_usage = await asyncio.get_event_loop().run_in_executor(
            executor,
            run_relevant_agent_sync,
            section,
            purpose,
            content,
            user_guidelines
        )
        return result, token_usage
    finally:
        executor.shutdown(wait=False)

async def get_valid_models():
    """Get a list of valid AI models with their display names and descriptions from MongoDB.

    This function retrieves the list of active AI models from the MongoDB app_config collection.
    It includes fallback to hardcoded models if the database is unavailable.

    Returns:
        list: A list of dictionaries with model information

    Example:
        >>> models = await get_valid_models()
        >>> for model in models:
        >>>     print(f"{model['display_name']} - {model['model_name']}")
    """
    try:
        from utils.database import app_config_collection

        # Get models configuration from MongoDB
        models_config = await app_config_collection.find_one({"config_type": "ai_models"})

        if models_config and "models" in models_config:
            # Filter only active models and return in the expected format
            active_models = [
                {
                    "model_name": model["model_name"],
                    "display_name": model["display_name"],
                    "description": model["description"]
                }
                for model in models_config["models"]
                if model.get("is_active", True)
            ]
            return active_models
        else:
            # Fallback to default models if no configuration found
            return _get_fallback_models()

    except Exception as e:
        # Log the error and fallback to hardcoded models
        import logging
        logger = logging.getLogger(__name__)
        logger.warning(f"Failed to retrieve models from MongoDB, using fallback: {e}")
        return _get_fallback_models()

def _get_fallback_models():
    """Fallback function that returns hardcoded models when MongoDB is unavailable.

    Returns:
        list: A list of dictionaries with default model information
    """
    return [
        {
            "model_name": "gemini-1.5-flash-001",
            "display_name": "Easy Bot",
            "description": "Fast and efficient model for basic document processing and summarization tasks."
        },
        {
            "model_name": "gemini-2.0-flash-exp",
            "display_name": "Smart Bot",
            "description": "Advanced model with improved comprehension and reasoning capabilities for complex documents."
        },
        {
            "model_name": "gemini-2.5-flash-preview-04-17",
            "display_name": "Power Bot",
            "description": "Experimental high-performance model with superior analytical capabilities for detailed document analysis."
        }
    ]

async def get_default_model():
    """Get the default AI model from MongoDB configuration.

    Returns:
        str: The model name of the default model, or fallback default if not found
    """
    try:
        from utils.database import app_config_collection
        from utils.constants import AIModels

        # Get models configuration from MongoDB
        models_config = await app_config_collection.find_one({"config_type": "ai_models"})

        if models_config and "models" in models_config:
            # Find the default model
            for model in models_config["models"]:
                if model.get("is_default", False) and model.get("is_active", True):
                    return model["model_name"]

            # If no default found, return the first active model
            for model in models_config["models"]:
                if model.get("is_active", True):
                    return model["model_name"]

        # Fallback to hardcoded default
        return AIModels.DEFAULT_MODEL

    except Exception as e:
        # Log the error and fallback to hardcoded default
        import logging
        logger = logging.getLogger(__name__)
        logger.warning(f"Failed to retrieve default model from MongoDB, using fallback: {e}")
        from utils.constants import AIModels
        return AIModels.DEFAULT_MODEL
