"""Content Management Utilities Module

This module provides functionality for managing content versions, including:
- Saving edited content with version history
- Managing version metadata in MongoDB
- Ensuring proper directory structure in GCS

Key Features:
- Version history management
- Active version tracking
- Automatic cleanup of old versions
- Directory structure management
"""

import json
import asyncio
import logging
from datetime import datetime, timezone
from utils.database import content_versions_collection, projects_collection
from utils.constants import StoragePaths, ContentTypes, VersionManagement, Sections
from utils.llm_config import get_llm_instance
from RAG.rfp_prompts import get_extract_metadata_from_edited_proposal_info_prompt
from utils.text_processing import parse_json_proposal_info

logger = logging.getLogger(__name__)

async def extract_metadata_from_edited_proposal_info(edited_markdown_content, project_manager):
    """Extract JSON metadata from edited Proposal Info markdown using LLM.

    Args:
        edited_markdown_content (str): The edited Proposal Info markdown content
        project_manager: ProjectManager instance

    Returns:
        tuple: (success: bool, filtered_metadata: dict, token_usage: dict)
    """
    try:
        logger.info("Extracting metadata from edited Proposal Info using LLM")

        # Get the LLM instance using the project's model
        llm = get_llm_instance(model_name=project_manager.model_name)

        # Get the prompt for extracting metadata
        prompt = get_extract_metadata_from_edited_proposal_info_prompt()
        full_prompt = f"{prompt}\n\n{edited_markdown_content}"

        logger.info(f"Sending prompt to LLM (model: {project_manager.model_name})")

        # Make the LLM call
        response = await llm.ainvoke(full_prompt)

        # Extract content and token usage
        if hasattr(response, 'content'):
            llm_response_content = response.content
        else:
            llm_response_content = str(response)

        # Track token usage
        input_tokens = 0
        output_tokens = 0
        if hasattr(response, 'usage_metadata'):
            input_tokens = getattr(response.usage_metadata, 'input_tokens', 0)
            output_tokens = getattr(response.usage_metadata, 'output_tokens', 0)
        elif hasattr(response, 'response_metadata'):
            usage = response.response_metadata.get('token_usage', {})
            input_tokens = usage.get('prompt_tokens', 0)
            output_tokens = usage.get('completion_tokens', 0)

        logger.info(f"LLM response received. Input tokens: {input_tokens}, Output tokens: {output_tokens}")

        # Parse the LLM response as JSON (already contains only required fields)
        try:
            filtered_metadata = parse_json_proposal_info(llm_response_content)
            logger.info(f"Successfully parsed LLM response into JSON with {len(filtered_metadata)} required fields")
        except Exception as parse_error:
            logger.error(f"Failed to parse LLM response as JSON: {str(parse_error)}")
            logger.error(f"LLM response content: {llm_response_content[:500]}...")
            return False, {}, {"input_tokens": input_tokens, "output_tokens": output_tokens}

        token_usage = {
            "input_tokens": input_tokens,
            "output_tokens": output_tokens
        }

        return True, filtered_metadata, token_usage

    except Exception as e:
        logger.error(f"Error extracting metadata from edited Proposal Info: {str(e)}")
        return False, {}, {"input_tokens": 0, "output_tokens": 0}

async def save_content_version(
    project_manager,
    content_type,
    content,
    user_id
):
    """Save edited content to the main files first, then create a versioned copy with active flag in MongoDB.

    This approach makes the main files the source of truth, with versions serving as historical snapshots.
    Descriptions are automatically generated with timestamps (e.g., "Version Jun 18, 2025 at 11:05 AM").

    Args:
        project_manager: ProjectManager instance
        content_type: Type of content ("outline", "summaries", or "response_content")
        content: List of dictionaries with 'section_title' and 'content' keys
        user_id: ID of the user making the change

    Returns:
        dict: Result of the save operation
    """
    # 1. Update the in-memory content first
    if content_type == ContentTypes.OUTLINE:
        # For outlines, content should have exactly one item containing the outline content
        outline_content = content[0]['content']  # Extract the actual outline content

        # Load existing outline data from GCP to preserve token counts
        outline_path = f"{project_manager.project_prefix}{StoragePaths.OUTLINE_FILE}"
        outline_blob = project_manager.bucket.blob(outline_path)

        # Try to load existing outline data
        existing_outline_data = {}
        try:
            existing_outline_text = await asyncio.to_thread(outline_blob.download_as_text)
            existing_outline_data = json.loads(existing_outline_text)
            logger.info(f"Loaded existing outline data for token preservation")
        except Exception as e:
            logger.warning(f"Could not load existing outline data: {str(e)}, using defaults")

        # Preserve existing token counts
        preserved_input_tokens = existing_outline_data.get("input_tokens", 0)
        preserved_output_tokens = existing_outline_data.get("output_tokens", 0)

        project_manager.outline_data = {
            "content": outline_content,
            "input_tokens": preserved_input_tokens,
            "output_tokens": preserved_output_tokens
        }

        logger.info(f"Updated outline content while preserving token counts: input={preserved_input_tokens}, output={preserved_output_tokens}")
    elif content_type == ContentTypes.RESPONSE_CONTENT:
        # For response content, content should have exactly one item with section_title "response_content"
        response_content = content[0]['content']  # Extract the actual response content

        # Load existing response content data from GCP to preserve token counts and metadata
        response_content_path = f"{project_manager.project_prefix}{StoragePaths.RESPONSE_CONTENT_FILE}"
        response_content_blob = project_manager.bucket.blob(response_content_path)

        # Try to load existing response content data
        existing_response_content_data = {}
        try:
            existing_response_content_text = await asyncio.to_thread(response_content_blob.download_as_text)
            existing_response_content_data = json.loads(existing_response_content_text)
            logger.info(f"Loaded existing response content data for token preservation")
        except Exception as e:
            logger.warning(f"Could not load existing response content data: {str(e)}, using defaults")

        # Preserve existing token counts and metadata
        preserved_input_tokens = existing_response_content_data.get("input_tokens", 0)
        preserved_output_tokens = existing_response_content_data.get("output_tokens", 0)
        preserved_outline_type = existing_response_content_data.get("outline_type", "")
        preserved_data_library_files = existing_response_content_data.get("data_library_files", [])

        project_manager.response_content_data = {
            "content": response_content,
            "input_tokens": preserved_input_tokens,
            "output_tokens": preserved_output_tokens,
            "outline_type": preserved_outline_type,
            "data_library_files": preserved_data_library_files
        }

        logger.info(f"Updated response content while preserving metadata and token counts: input={preserved_input_tokens}, output={preserved_output_tokens}, outline_type={preserved_outline_type}")
    elif content_type == ContentTypes.SUMMARIES:
        # For summaries, process each section in the content list
        if not hasattr(project_manager, 'responses') or project_manager.responses is None:
            project_manager.responses = {}

        # Process each section in the content list
        for section_item in content:
            section_title = section_item['section_title']
            section_content = section_item['content']

            # Ensure section title has correct capitalization (should already be validated)
            correct_section_title = section_title
            for standard_section in Sections.ALL_SECTIONS:
                if section_title.lower() == standard_section.lower():
                    correct_section_title = standard_section
                    break

            # Update the in-memory content with correct capitalization, preserving existing token information
            existing_response = project_manager.responses.get(correct_section_title, {})
            preserved_input_tokens = existing_response.get("input_tokens", 0)
            preserved_output_tokens = existing_response.get("output_tokens", 0)
            preserved_vector_db_flag = existing_response.get("used_vector_db", False)

            project_manager.responses[correct_section_title] = {
                "content": section_content,
                "input_tokens": preserved_input_tokens,
                "output_tokens": preserved_output_tokens,
                "used_vector_db": preserved_vector_db_flag
            }

            logger.info(f"Updated section '{correct_section_title}' content while preserving token counts: input={preserved_input_tokens}, output={preserved_output_tokens}")

            # If the section title was corrected and the old one exists, remove it to avoid duplicates
            if correct_section_title != section_title and section_title in project_manager.responses:
                logger.info(f"Removing duplicate section with incorrect capitalization: '{section_title}'")
                del project_manager.responses[section_title]

        # Check if Proposal Info was edited and update rfp_project_metadata if needed
        proposal_info_edited = False
        proposal_info_new_content = None

        for section_item in content:
            if section_item['section_title'] == Sections.PROPOSAL_INFO:
                proposal_info_new_content = section_item['content']

                # Get the original Proposal Info content from summaries.json (before editing)
                original_proposal_info = ""
                try:
                    # Load the current summaries.json from GCS to compare
                    summaries_path = f"{project_manager.project_prefix}{StoragePaths.SUMMARIES_FILE}"
                    summaries_blob = project_manager.bucket.blob(summaries_path)
                    current_summaries_text = await asyncio.to_thread(summaries_blob.download_as_text)
                    current_summaries = json.loads(current_summaries_text)
                    original_proposal_info = current_summaries.get(Sections.PROPOSAL_INFO, {}).get("content", "")
                except Exception as e:
                    logger.warning(f"Could not load original summaries for comparison: {str(e)}")
                    original_proposal_info = ""

                # Compare original and new content
                if original_proposal_info.strip() != proposal_info_new_content.strip():
                    proposal_info_edited = True
                    logger.info("Proposal Info content has been edited, will extract metadata using LLM")
                else:
                    logger.info("Proposal Info content unchanged, no metadata extraction needed")
                break

    # 2. Update the main files first (source of truth)
    logger.info(f"Updating main files for {content_type}")

    if content_type == ContentTypes.OUTLINE:
        # Update the main outline.json file
        outline_path = f"{project_manager.project_prefix}{StoragePaths.OUTLINE_FILE}"
        outline_blob = project_manager.bucket.blob(outline_path)

        # Save the in-memory outline data to the main file
        try:
            await asyncio.to_thread(outline_blob.upload_from_string, json.dumps(project_manager.outline_data, indent=2))
            logger.info(f"Successfully updated main outline file for project {project_manager.project_id}")
        except Exception as e:
            logger.error(f"Error updating main outline file: {str(e)}")
            raise
    elif content_type == ContentTypes.RESPONSE_CONTENT:
        # Update the main response_content.json file
        response_content_path = f"{project_manager.project_prefix}{StoragePaths.RESPONSE_CONTENT_FILE}"
        response_content_blob = project_manager.bucket.blob(response_content_path)

        # Save the in-memory response content data to the main file
        try:
            await asyncio.to_thread(response_content_blob.upload_from_string, json.dumps(project_manager.response_content_data, indent=2))
            logger.info(f"Successfully updated main response content file for project {project_manager.project_id}")
        except Exception as e:
            logger.error(f"Error updating main response content file: {str(e)}")
            raise
    else:  # summaries
        # Update the main summaries.json file
        summaries_path = f"{project_manager.project_prefix}{StoragePaths.SUMMARIES_FILE}"
        summaries_blob = project_manager.bucket.blob(summaries_path)

        # Save the in-memory responses to the main file
        try:
            await asyncio.to_thread(summaries_blob.upload_from_string, json.dumps(project_manager.responses, indent=2))
            logger.info(f"Updated main summaries file with all sections")
        except Exception as e:
            logger.error(f"Error updating main summaries file: {str(e)}")
            raise

        # Handle Proposal Info metadata extraction if content was edited
        if proposal_info_edited and proposal_info_new_content:
            logger.info("Processing edited Proposal Info to extract and update rfp_project_metadata")

            try:
                # Extract metadata using LLM
                success, filtered_metadata, token_usage = await extract_metadata_from_edited_proposal_info(
                    proposal_info_new_content, project_manager
                )

                if success:
                    # Update MongoDB with the new filtered metadata
                    update_result = await projects_collection.update_one(
                        {"project_id": project_manager.project_id},
                        {"$set": {"rfp_project_metadata": filtered_metadata}}
                    )

                    logger.info(f"Successfully updated rfp_project_metadata in MongoDB. Matched: {update_result.matched_count}, Modified: {update_result.modified_count}")

                    # Update token counts for Proposal Info section in summaries.json
                    if Sections.PROPOSAL_INFO in project_manager.responses:
                        current_response = project_manager.responses[Sections.PROPOSAL_INFO]
                        current_input_tokens = current_response.get("input_tokens", 0)
                        current_output_tokens = current_response.get("output_tokens", 0)

                        # Add the new token usage to existing counts
                        new_input_tokens = current_input_tokens + token_usage.get("input_tokens", 0)
                        new_output_tokens = current_output_tokens + token_usage.get("output_tokens", 0)

                        project_manager.responses[Sections.PROPOSAL_INFO]["input_tokens"] = new_input_tokens
                        project_manager.responses[Sections.PROPOSAL_INFO]["output_tokens"] = new_output_tokens

                        logger.info(f"Updated token counts for Proposal Info: input={new_input_tokens} (+{token_usage.get('input_tokens', 0)}), output={new_output_tokens} (+{token_usage.get('output_tokens', 0)})")

                        # Re-save summaries.json with updated token counts
                        try:
                            await asyncio.to_thread(summaries_blob.upload_from_string, json.dumps(project_manager.responses, indent=2))
                            logger.info("Updated summaries.json with new token counts")
                        except Exception as save_error:
                            logger.error(f"Error re-saving summaries.json with token counts: {str(save_error)}")
                else:
                    logger.error("Failed to extract metadata from edited Proposal Info, keeping existing rfp_project_metadata")

            except Exception as metadata_error:
                logger.error(f"Error processing edited Proposal Info metadata: {str(metadata_error)}")
                logger.error("Keeping existing rfp_project_metadata unchanged")

    # 3. Create timestamp for the version
    timestamp = datetime.now().strftime(VersionManagement.TIMESTAMP_FORMAT)

    # 4. Determine history file path
    if content_type == ContentTypes.OUTLINE:
        history_path = f"{project_manager.project_prefix}{StoragePaths.OUTLINE_HISTORY_TEMPLATE.format(timestamp=timestamp)}"
    elif content_type == ContentTypes.RESPONSE_CONTENT:
        history_path = f"{project_manager.project_prefix}{StoragePaths.RESPONSE_CONTENT_HISTORY_TEMPLATE.format(timestamp=timestamp)}"
    else:  # summaries
        history_path = f"{project_manager.project_prefix}{StoragePaths.SUMMARIES_HISTORY_TEMPLATE.format(timestamp=timestamp)}"

    # 5. Ensure history directories exist
    await ensure_history_directories(project_manager)

    # 6. Create a version in the history folder (snapshot of the main file)
    history_blob = project_manager.bucket.blob(history_path)
    try:
        if content_type == ContentTypes.OUTLINE:
            # Save a copy of the outline data to the history folder
            await asyncio.to_thread(history_blob.upload_from_string, json.dumps(project_manager.outline_data, indent=2))
        elif content_type == ContentTypes.RESPONSE_CONTENT:
            # Save a copy of the response content data to the history folder
            await asyncio.to_thread(history_blob.upload_from_string, json.dumps(project_manager.response_content_data, indent=2))
        else:  # summaries
            # Save a copy of the responses to the history folder
            await asyncio.to_thread(history_blob.upload_from_string, json.dumps(project_manager.responses, indent=2))

        logger.info(f"Created version snapshot in history folder: {history_path}")
    except Exception as e:
        logger.error(f"Error creating version snapshot: {str(e)}")
        raise

    # 7. Generate automatic description with timestamp
    # Create a user-friendly timestamp for the version description
    current_time = datetime.now(timezone.utc)
    display_timestamp = current_time.strftime(VersionManagement.DISPLAY_TIMESTAMP_FORMAT)
    description = f"Version {display_timestamp}"

    logger.info(f"Generated version description: {description}")

    # 8. Update MongoDB - set previous active version to inactive
    await content_versions_collection.update_many(
        {"project_id": project_manager.project_id, "content_type": content_type, VersionManagement.ACTIVE_FLAG: True},
        {"$set": {VersionManagement.ACTIVE_FLAG: False}}
    )

    # 9. Save new version metadata to MongoDB with active flag
    version_doc = {
        "project_id": project_manager.project_id,
        "content_type": content_type,
        "file_path": history_path,
        "created_at": datetime.now(),
        "created_by": user_id,
        VersionManagement.ACTIVE_FLAG: True,
        "description": description
    }
    await content_versions_collection.insert_one(version_doc)

    # 10. Check if we need to delete old versions
    versions = await content_versions_collection.find(
        {"project_id": project_manager.project_id, "content_type": content_type}
    ).sort("created_at", -1).to_list(length=VersionManagement.MAX_VERSIONS + 1)  # Get one more than our limit

    if len(versions) > VersionManagement.MAX_VERSIONS:
        oldest_version = versions[VersionManagement.MAX_VERSIONS]  # The version that exceeds our limit
        # Delete from GCS
        oldest_blob = project_manager.bucket.blob(oldest_version["file_path"])
        await asyncio.to_thread(oldest_blob.delete)
        # Delete from MongoDB
        await content_versions_collection.delete_one({"_id": oldest_version["_id"]})
        logger.info(f"Deleted old version: {oldest_version['file_path']}")

    # 11. Return the result
    return {
        "success": True,
        "message": f"Content saved successfully as {description}",
        "version_info": {
            "description": description,
            "created_at": datetime.now().isoformat()
        },
        "versions_count": min(len(versions), VersionManagement.MAX_VERSIONS),
        "max_versions": VersionManagement.MAX_VERSIONS
    }

async def ensure_history_directories(project_manager):
    """Ensure the history directories exist in GCS.

    Args:
        project_manager: ProjectManager instance
    """
    outline_dir = f"{project_manager.project_prefix}{StoragePaths.OUTLINE_HISTORY_FOLDER}"
    summaries_dir = f"{project_manager.project_prefix}{StoragePaths.SUMMARIZATIONS_HISTORY_FOLDER}"
    response_content_dir = f"{project_manager.project_prefix}{StoragePaths.RESPONSE_CONTENT_HISTORY_FOLDER}"

    # Create empty placeholder files to ensure directories exist
    outline_placeholder = project_manager.bucket.blob(f"{outline_dir}{StoragePaths.PLACEHOLDER_SUFFIX}")
    summaries_placeholder = project_manager.bucket.blob(f"{summaries_dir}{StoragePaths.PLACEHOLDER_SUFFIX}")
    response_content_placeholder = project_manager.bucket.blob(f"{response_content_dir}{StoragePaths.PLACEHOLDER_SUFFIX}")

    await asyncio.gather(
        asyncio.to_thread(outline_placeholder.upload_from_string, ""),
        asyncio.to_thread(summaries_placeholder.upload_from_string, ""),
        asyncio.to_thread(response_content_placeholder.upload_from_string, "")
    )
