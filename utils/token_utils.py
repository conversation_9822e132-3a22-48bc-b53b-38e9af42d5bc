"""Token Counting Utilities Module

This module provides functionality for counting tokens in text strings using different tokenization
methods based on the language model being used. It supports both Vertex AI's Gemini models and
other models that use tiktoken for tokenization.

Key Features:
- Model-specific token counting
- Support for Gemini and non-Gemini models
- Error handling and logging
- Zero-fallback for errors
"""

import logging
import re
import tiktoken

def count_tokens(text, model="gpt-3.5-turbo"):
    """Count the number of tokens in a text string.

    This function counts tokens differently based on the model:
    - For Gemini models, it uses a simple approximation (words / 0.6)
    - For other models, it uses tiktoken for accurate counting

    Args:
        text (str): The text to count tokens for
        model (str): The model to count tokens for (default: "gpt-3.5-turbo")

    Returns:
        int: The number of tokens in the text
    """
    if not text:
        return 0

    try:
        # For Gemini models, use approximation
        if model.startswith("gemini"):
            # Gemini models use a different tokenization method
            # Approximation: words / 0.6 (based on empirical testing)
            words = len(re.findall(r'\b\w+\b', text))
            return int(words / 0.6)
        else:
            # For other models, use tiktoken
            encoding = tiktoken.encoding_for_model(model)
            return len(encoding.encode(text))
    except Exception as e:
        logging.error(f"Error counting tokens: {str(e)}")
        # Fallback to a very simple approximation
        return len(text) // 4  # Very rough approximation
