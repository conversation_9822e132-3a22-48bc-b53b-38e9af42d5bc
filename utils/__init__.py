"""Utilities Module

This module provides various utility functions for the application including:
- Token counting for different language models
- Content version management
- Text processing and extraction
- File handling utilities

Key Features:
- Model-specific token counting
- Content version management with history
- Support for Gemini and non-Gemini models
- Error handling and logging
- Zero-fallback for errors
"""

# Re-export all functions from the modules to maintain backward compatibility

# File processing utilities
from utils.file_processing import (
    identify_special_pages,
    read_docx,
    read_excel,
    is_blank_image
)

# Text processing utilities
from utils.text_processing import (
    clean_text_content,  # New unified function
    clean_markdown_header,  # Legacy - use clean_text_content instead
    strip_markdown_delimiters,  # Legacy - use clean_text_content instead
    clean_json_code_block,  # Legacy - use clean_text_content instead
    clean_markdown_code_block,  # Legacy - use clean_text_content instead
    parse_json_proposal_info,
    filter_rfp_project_metadata,
    convert_proposal_info_to_markdown
)

# PDF utilities
from utils.pdf_utils import (
    is_table_dominant_page,
    has_solicitation_content,
    extract_text_with_tesseract,
    extract_text_from_image,
    extract_markdown_from_pdf,
    extract_sections
)

# Content management utilities
from utils.content_management import (
    save_content_version,
    ensure_history_directories,
    extract_metadata_from_edited_proposal_info
)

# Project utilities
from utils.project_utils import (
    get_combined_content_from_processed_doc,
    is_relevant_document,
    run_relevant_agent_sync,
    run_relevant_agent,
    get_valid_models
)

# Constants
from utils.constants import *

# LLM Configuration
from utils.llm_config import get_llm_instance

# Authentication
from utils.auth import (
    get_current_active_user, User, Token, get_password_hash,
    authenticate_user, create_access_token, get_user_by_email,
    verify_password_reset_token, create_password_reset_token,
    update_user_password, check_password_reset_tokens,
    verify_email_token, create_verification_token, is_admin,
    ACCESS_TOKEN_EXPIRE_MINUTES
)

# Database
from utils.database import (
    db, users_collection, projects_collection, verification_tokens_collection,
    password_reset_tokens_collection, content_versions_collection,
    app_config_collection, setup_database
)

# Import RAG functions where they are used to avoid circular imports
extract_page_as_image = None
extract_text_with_vision = None

def _load_rag_functions():
    global extract_page_as_image, extract_text_with_vision
    if extract_page_as_image is None:
        from RAG.documents_loader import extract_page_as_image, extract_text_with_vision
