"""Text Processing Utilities Module

This module provides functionality for processing and manipulating text content, including:
- Markdown cleaning and formatting
- JSON parsing and extraction
- Text filtering and transformation

Key Features:
- Markdown header cleaning
- JSON proposal info parsing
- Metadata filtering
- Format conversion
"""

import logging
import re
import json
from typing import Any

def clean_text_content(text: str, mode: str = 'markdown', return_json: bool = False) -> Any:
    """
    Unified text processing function that handles various text cleaning operations.

    Args:
        text (str): The text content to clean
        mode (str): Processing mode - 'markdown', 'header', 'json', or 'auto'
            - 'markdown': Remove markdown code fences and clean HTML/comments
            - 'header': Find ## headers and clean content after them
            - 'json': Extract and parse JSON from code blocks
            - 'auto': Automatically detect and apply appropriate cleaning
        return_json (bool): If True and JSON is found, return parsed JSON object

    Returns:
        str or Any: Cleaned text string, or parsed JSON object if return_json=True

    Raises:
        ValueError: If mode='json' or return_json=True but no valid JSON is found
    """
    if not text or not isinstance(text, str):
        return text

    # Handle header mode - find ## and process from there
    if mode == 'header':
        header_pos = text.find('##')
        if header_pos > 0:
            text = text[header_pos:]

    # Handle JSON extraction mode or auto-detection
    if mode == 'json' or (mode == 'auto' and '```json' in text) or return_json:
        # Use regex to match content between ```json and ```
        json_match = re.search(r'```json\s*(.*?)\s*```', text, re.DOTALL)
        if json_match:
            json_content = json_match.group(1)
            try:
                return json.loads(json_content)
            except json.JSONDecodeError as e:
                if mode == 'json':
                    raise ValueError(f"Invalid JSON content found in code block: {str(e)}")
                elif return_json:
                    # If return_json is True but JSON is invalid, fall through to text processing
                    pass
        elif mode == 'json':
            raise ValueError("No JSON code block found in the provided text.")
        elif return_json and '```json' not in text:
            # If return_json is True but no JSON block found, fall through to text processing
            pass

    # Strip whitespace
    text = text.strip()

    # Remove markdown code fence delimiters
    # Handle any code fence pattern: ```format_name or just ```
    # Remove from ``` to the first whitespace (or end if no whitespace)
    if text.startswith('```'):
        # Find the first whitespace character after ```
        match = re.match(r'^```[^\s]*\s*', text)
        if match:
            text = text[len(match.group(0)):]
        else:
            # If no whitespace found, just remove the ``` part
            text = text[3:]

    # Strip whitespace again after removing leading delimiters
    text = text.strip()

    # Remove trailing markdown delimiters
    if text.endswith('```'):
        text = text[:-3].rstrip()

    # Handle any code fence pattern anywhere in text: ```format_name or just ```
    # Remove from ``` to the first whitespace (or end if no whitespace)
    text = re.sub(r'```[^\s]*\s*', '', text)

    # Replace HTML line breaks with newlines
    text = re.sub(r'<br\s*/?>', '\n', text, flags=re.IGNORECASE)

    # Replace double slashes with newlines (LLM sometimes uses // for line breaks)
    # Handle both "//" and "// " patterns
    text = re.sub(r'//\s*', '\n', text)

    # Replace * with - for bullet points (only at start of line or after whitespace)
    # This avoids matching ** markdown bold formatting
    text = re.sub(r'(^|\n)\*\s+', r'\1- ', text)

    # Remove HTML comments
    # text = re.sub(r'<!--irrelevant:.*?-->', '', text, flags=re.DOTALL)
    # text = re.sub(r'<!--.*?-->', '', text, flags=re.DOTALL)

    # Ensure colons have whitespace before and after them
    # This pattern matches colons that don't have whitespace before or after
    # and adds appropriate spacing while preserving existing proper spacing
    text = re.sub(r'(\S):(\S)', r'\1 : \2', text)  # Add space before and after when missing both
    text = re.sub(r'(\S):(\s)', r'\1 :\2', text)   # Add space before when missing
    text = re.sub(r'(\s):(\S)', r'\1: \2', text)   # Add space after when missing

    # Normalize multiple consecutive newlines to maximum of two newlines
    text = re.sub(r'\n{3,}', '\n\n', text)

    return text.strip()


# Backward compatibility functions - these now use the unified function
def clean_markdown_header(text):
    """Legacy function - use clean_text_content(text, mode='header') instead."""
    return clean_text_content(text, mode='header')


def strip_markdown_delimiters(outline_text):
    """Legacy function - use clean_text_content(text, mode='markdown') instead."""
    return clean_text_content(outline_text, mode='markdown')


def clean_json_code_block(text: str) -> Any:
    """Legacy function - use clean_text_content(text, mode='json') instead."""
    return clean_text_content(text, mode='json')


def clean_markdown_code_block(text: str) -> str:
    """Legacy function - use clean_text_content(text, mode='markdown') instead."""
    return clean_text_content(text, mode='markdown')

def parse_json_proposal_info(text):
    """Parse the JSON output from the RFP info extraction.

    This function takes the raw output from the model, cleans it by removing any
    markdown formatting or code block markers, and returns a properly parsed JSON object.

    Args:
        text (str): The raw text output from the model containing JSON data

    Returns:
        dict: A Python dictionary containing the parsed JSON data

    Raises:
        json.JSONDecodeError: If the text cannot be parsed as valid JSON
    """
    # Log the original text for debugging
    logging.debug(f"Original text to parse: {text[:100]}...")

    # Clean the text by removing any markdown code block markers
    # First, check if the text is wrapped in ```json ... ``` or just ``` ... ```
    json_pattern = r'```(?:json)?\s*(\{.*\})\s*```'
    match = re.search(json_pattern, text, re.DOTALL)

    if match:
        # Extract just the JSON part from between the code block markers
        cleaned_text = match.group(1)
        logging.debug("Found JSON inside code block markers")
    else:
        # If no code block markers, try to find JSON object directly
        # Look for text that starts with { and ends with }
        json_direct_pattern = r'(\{.*\})'
        match = re.search(json_direct_pattern, text, re.DOTALL)

        if match:
            cleaned_text = match.group(1)
            logging.debug("Found JSON object directly in text")
        else:
            # If we can't find a clear JSON pattern, just use the original text
            cleaned_text = text.strip()
            logging.debug("No clear JSON pattern found, using original text")

    # Try to parse the cleaned text as JSON
    try:
        result = json.loads(cleaned_text)
        logging.info(f"Successfully parsed JSON with {len(result)} fields")
        return result
    except json.JSONDecodeError as e:
        # If parsing fails, try more aggressive cleaning
        logging.warning(f"Initial JSON parsing failed: {str(e)}")

        # Try to find anything that looks like a JSON object
        try:
            # Find the first { and the last }
            start = cleaned_text.find('{')
            end = cleaned_text.rfind('}')

            if start != -1 and end != -1 and end > start:
                json_candidate = cleaned_text[start:end+1]
                result = json.loads(json_candidate)
                logging.info(f"Successfully parsed JSON after aggressive cleaning with {len(result)} fields")
                return result
            else:
                logging.error("Could not find valid JSON object markers")
                raise
        except Exception as e2:
            logging.error(f"JSON parsing failed after aggressive cleaning: {str(e2)}")
            raise

def filter_rfp_project_metadata(metadata):
    """Filter the rfp_project_metadata to only include specific fields.

    This function takes the full rfp_project_metadata dictionary and returns a new
    dictionary containing only the specified fields.

    Args:
        metadata (dict): The full rfp_project_metadata dictionary

    Returns:
        dict: A filtered dictionary containing only the specified fields
    """
    # Define the fields to keep
    fields_to_keep = [
        "Solicitation Number",
        "Title",
        "Solicitation Type",
        "Questions Due Date",
        "Proposal Due Date",
        "POC"
    ]

    # Create a new dictionary with only the specified fields
    filtered_metadata = {}

    for field in fields_to_keep:
        if field in metadata:
            filtered_metadata[field] = metadata[field]
        else:
            # Try to find similar field names
            for key in metadata:
                if field.lower() in key.lower():
                    filtered_metadata[field] = metadata[key]
                    logging.info(f"Mapped '{key}' to '{field}'")
                    break

    logging.info(f"Filtered rfp_project_metadata from {len(metadata)} to {len(filtered_metadata)} fields")
    return filtered_metadata

def convert_proposal_info_to_markdown(proposal_info):
    """Convert proposal info JSON to markdown bullet point format.

    This function takes the proposal info JSON and converts it to a markdown bullet point
    format for rendering on the frontend.

    Args:
        proposal_info (dict): The proposal info JSON

    Returns:
        str: Markdown bullet point representation of the proposal info
    """
    if not proposal_info:
        logging.warning("Empty proposal info provided to convert_proposal_info_to_markdown")
        return "## Proposal Info\n\nNo proposal information available."

    # Start with the header
    markdown = "## Proposal Info\n\n"

    # Function to format complex values for bullet points
    def format_value(val, parent_key=None, indent_level=0):
        indent = "  " * indent_level  # Two spaces per indent level

        if isinstance(val, dict):
            # Special handling for common nested structures
            if parent_key == "Period of Performance":
                # Format Period of Performance specially
                formatted = ""

                # Handle base period
                if "Base Period Duration" in val:
                    formatted += f"\n{indent}  - **Base Period:**\n"
                    formatted += f"{indent}    - Duration: {val.get('Base Period Duration', '')}\n"
                    formatted += f"{indent}    - Start Date: {val.get('Base Period Start Date', '')}\n"
                    formatted += f"{indent}    - End Date: {val.get('Base Period End Date', '')}"
                elif "duration" in val:
                    formatted += f"\n{indent}  - **Base Period:**\n"
                    formatted += f"{indent}    - Duration: {val.get('duration', '')}\n"
                    formatted += f"{indent}    - Start Date: {val.get('start_date', '')}\n"
                    formatted += f"{indent}    - End Date: {val.get('end_date', '')}"

                # Handle option periods
                if "option_periods" in val and isinstance(val["option_periods"], list):
                    formatted += f"\n{indent}  - **Option Periods:**"
                    for i, period in enumerate(val["option_periods"], 1):
                        if isinstance(period, dict):
                            formatted += f"\n{indent}    - {period.get('name', f'Option Period {i}')}: "
                            formatted += f"Duration: {period.get('duration', '')}, "
                            formatted += f"Start Date: {period.get('start_date', '')}, "
                            formatted += f"End Date: {period.get('end_date', '')}"

                # Handle extensions
                if "extensions" in val and isinstance(val["extensions"], list):
                    formatted += f"\n{indent}  - **Extensions:**"
                    for i, ext in enumerate(val["extensions"], 1):
                        if isinstance(ext, dict):
                            formatted += f"\n{indent}    - {ext.get('name', f'Extension {i}')}: "
                            formatted += f"Duration: {ext.get('duration', '')}, "
                            formatted += f"Start Date: {ext.get('start_date', '')}, "
                            formatted += f"End Date: {ext.get('end_date', '')}"

                return formatted

            elif parent_key == "POC":
                # Format POC specially
                formatted = ""

                # Handle nested Primary POC
                if "Primary" in val and isinstance(val["Primary"], dict):
                    primary = val["Primary"]
                    formatted += f"\n{indent}  - **Primary Contact:**\n"
                    formatted += f"{indent}    - Name: {primary.get('Name', '')}\n"
                    formatted += f"{indent}    - Title: {primary.get('Title', '')}\n"
                    formatted += f"{indent}    - Email: {primary.get('email', '')}\n"
                    formatted += f"{indent}    - Phone: {primary.get('phone', '')}"

                # Handle nested secondary POC
                if "secondary" in val and isinstance(val["secondary"], dict):
                    secondary = val["secondary"]
                    formatted += f"\n{indent}  - **Secondary Contact:**\n"
                    formatted += f"{indent}    - Name: {secondary.get('name', '')}\n"
                    formatted += f"{indent}    - Title: {secondary.get('title', '')}\n"
                    formatted += f"{indent}    - Email: {secondary.get('email', '')}\n"
                    formatted += f"{indent}    - Phone: {secondary.get('phone', '')}"

                # Fallback for flat structure
                if not formatted:
                    # Try flat structure
                    if "Primary Name" in val:
                        formatted += f"\n{indent}  - **Primary Contact:**\n"
                        formatted += f"{indent}    - Name: {val.get('Primary Name', '')}\n"
                        formatted += f"{indent}    - Title: {val.get('Primary Title', '')}\n"
                        formatted += f"{indent}    - Email: {val.get('Primary email', '')}\n"
                        formatted += f"{indent}    - Phone: {val.get('Primary phone', '')}"

                    if "secondary name" in val:
                        formatted += f"\n{indent}  - **Secondary Contact:**\n"
                        formatted += f"{indent}    - Name: {val.get('secondary name', '')}\n"
                        formatted += f"{indent}    - Title: {val.get('secondary title', '')}\n"
                        formatted += f"{indent}    - Email: {val.get('secondary email', '')}\n"
                        formatted += f"{indent}    - Phone: {val.get('secondary phone', '')}"

                return formatted

            # Default dictionary formatting
            formatted = ""
            for k, v in val.items():
                # Skip keys that will be handled specially
                if parent_key == "Period of Performance" and k in ["option_periods", "extensions"]:
                    continue

                # Recursively format nested values
                formatted_v = format_value(v, k, indent_level + 1)
                formatted += f"\n{indent}  - **{k}:** {formatted_v}"
            return formatted

        elif isinstance(val, list):
            # Format list as bullet points
            if not val:
                return ""

            # Special handling for Key Personnel
            if parent_key == "Key Personnel":
                if len(val) == 1:
                    return str(val[0])
                formatted = ""
                for item in val:
                    formatted += f"\n{indent}  - {item}"
                return formatted

            # Check if list contains dictionaries
            if any(isinstance(item, dict) for item in val):
                # Format list of dictionaries
                formatted = ""
                for i, item in enumerate(val, 1):
                    if isinstance(item, dict):
                        formatted += f"\n{indent}  - **Item {i}:**"
                        for k, v in item.items():
                            formatted_v = format_value(v, k, indent_level + 1)
                            formatted += f"\n{indent}    - {k}: {formatted_v}"
                    else:
                        formatted += f"\n{indent}  - {item}"
                return formatted
            else:
                # Simple list - format as bullet points
                if len(val) == 1:
                    return str(val[0])
                formatted = ""
                for item in val:
                    formatted += f"\n{indent}  - {item}"
                return formatted
        else:
            # Return string representation for other types
            return str(val)

    # Add each field as bullet points
    for key, value in proposal_info.items():
        # Format the value based on its type and parent key
        formatted_value = format_value(value, key)

        # Add the bullet point
        if formatted_value and formatted_value.strip():
            # If the formatted value starts with newlines (nested structure), use it as-is
            if formatted_value.startswith('\n'):
                markdown += f"- **{key}:**{formatted_value}\n\n"
            else:
                # Simple value, add it inline
                markdown += f"- **{key}:** {formatted_value}\n\n"
        else:
            # Empty or None value
            markdown += f"- **{key}:** Not specified\n\n"

    logging.info(f"Converted proposal info JSON with {len(proposal_info)} fields to markdown bullet points")
    return markdown
