"""Language Model Configuration Module

This module handles the configuration and initialization of Language Learning Models (LLMs)
for the document processing system. It supports both standard Vertex AI models and
experimental Google Generative AI models.

Features:
- Centralized LLM configuration management
- Support for multiple model types (Vertex AI and Generative AI)
- Default and custom configuration options
- Experimental model handling
- Logging of model initialization and configuration

The module provides a unified interface for getting configured LLM instances
while handling the differences between model types transparently.
"""

from langchain_google_vertexai import ChatVertexAI
from langchain_google_genai import GoogleGenerativeAI
from typing import Dict, Optional, Union
import logging

# Configure module logger
logger = logging.getLogger(__name__)

# Default model configuration
DEFAULT_MODEL_NAME = "gemini-1.5-flash-001"
DEFAULT_CONFIG = {
    "temperature": 0.2,      # Controls randomness in the output (0.0 = deterministic)
    "top_p": 0.95,          # Nucleus sampling parameter
    "top_k": 40,            # Top-k sampling parameter
    # "max_output_tokens": 8000,  # Maximum length of generated text
    "max_retries": 3        # Number of retry attempts for failed requests
}

# List of experimental model identifiers
EXPERIMENTAL_MODELS = ["gemini-2.5-flash-preview-04-17"]

def get_llm_instance(
    model_name: Optional[str] = None,
    custom_config: Optional[Dict] = None
) -> Union[ChatVertexAI, GoogleGenerativeAI]:
    """Get a configured LLM instance with specified or default settings.
    
    This function creates and configures either a ChatVertexAI or GoogleGenerativeAI
    instance based on the requested model name. It handles the configuration differences
    between standard and experimental models.
    
    Args:
        model_name: Optional model name to use (defaults to gemini-1.5-flash-001)
        custom_config: Optional dictionary of configuration settings to override defaults
        
    Returns:
        Union[ChatVertexAI, GoogleGenerativeAI]: Configured LLM instance
        
    Example:
        >>> llm = get_llm_instance("gemini-1.5-flash-001")
        >>> llm = get_llm_instance("gemini-exp-1206", {"temperature": 0.8})
    """
    model_name = model_name or DEFAULT_MODEL_NAME
    
    # Prepare configuration by merging defaults with custom settings
    config = DEFAULT_CONFIG.copy()
    if custom_config:
        config.update(custom_config)
    
    if model_name in EXPERIMENTAL_MODELS:
        # For experimental models, return GenerativeModel instance with config
        llm = GoogleGenerativeAI(
            model=model_name,
            temperature=config["temperature"],
            top_p=config["top_p"],
            top_k=config["top_k"],
            # max_output_tokens=config["max_output_tokens"],
            google_api_key="AIzaSyDI48dnX-LSWnbUgBt5HD7dwxGllRrM-T8"
        )
        logger.info(f"Using experimental model: {llm}")
        return llm
    
    # For standard models, create ChatVertexAI instance with full config
    llm = ChatVertexAI(
        model=model_name,
        **config
    ) 

    logger.info(f"Using ChatVertexAI model: {llm}")
    return llm
