"""Content Review Module

This module provides functionality for retrieving content review rulebook data from Google Cloud Storage.
"""

import json
import logging
import pickle
import asyncio
from typing import Optional, Union, Dict, Any, List
from google.cloud import storage
from google.cloud.exceptions import NotFound
from config import settings
from utils.constants import StoragePaths, DocumentTags
from utils.llm_config import get_llm_instance
from utils.token_utils import count_tokens
from utils.database import projects_collection
from RAG.rfp_prompts import get_rfp_specific_review_prompt

logger = logging.getLogger(__name__)


def get_content_review_rulebook(criterias_type: Optional[str] = None) -> Union[Dict[str, Any], List[Dict[str, Any]]]:
    """Retrieve the content review rulebook from Google Cloud Storage.

    Args:
        criterias_type (Optional[str]): Optional parameter to filter by criteria type.
                                      If provided, returns only the value for that key.
                                      Expected values: "proposal_criterias" or "general_criterias"

    Returns:
        Union[Dict[str, Any], List[Dict[str, Any]]]: The complete rulebook content or filtered content based on criterias_type

    Raises:
        NotFound: If the rulebook file is not found in GCS
        json.JSONDecodeError: If the file content is not valid JSON
        Exception: For other GCS or processing errors
    """
    try:
        # Initialize GCS client
        storage_client = storage.Client()
        bucket = storage_client.bucket(settings.BUCKET_NAME)

        # Construct the file path
        file_path = f"{StoragePaths.CONTENT_REVIEW_RULEBOOK_FOLDER}{StoragePaths.CONTENT_REVIEW_RULEBOOK_FILE}"

        # Get the blob reference
        blob = bucket.blob(file_path)

        # Download and parse the JSON content
        rulebook_text = blob.download_as_text()
        rulebook_data = json.loads(rulebook_text)

        logger.info(f"Successfully retrieved content review rulebook from GCS: {file_path}")

        # Return filtered content if criterias_type is specified
        if criterias_type:
            if criterias_type in rulebook_data:
                logger.info(f"Returning filtered content for criterias_type: {criterias_type}")
                return rulebook_data[criterias_type]
            else:
                logger.warning(f"Criterias type '{criterias_type}' not found in rulebook. Available keys: {list(rulebook_data.keys())}")
                return []

        # Return complete rulebook if no filter specified
        return rulebook_data

    except NotFound:
        logger.error(f"Content review rulebook file not found at: {file_path}")
        raise NotFound(f"Content review rulebook file not found in GCS")
    except json.JSONDecodeError as e:
        logger.error(f"Invalid JSON format in content review rulebook: {str(e)}")
        raise json.JSONDecodeError(f"Invalid JSON format in content review rulebook: {str(e)}")
    except Exception as e:
        logger.error(f"Error retrieving content review rulebook from GCS: {str(e)}")
        raise Exception(f"Error retrieving content review rulebook: {str(e)}")


async def generate_proposal_specific_criteria(project_manager) -> Dict[str, Any]:
    """Generate proposal-specific criteria using LLM based on Main Document content.

    Args:
        project_manager: ProjectManager instance for the project

    Returns:
        Dict[str, Any]: Generated criteria with content, input_tokens, and output_tokens

    Raises:
        Exception: If no Main Document found, content retrieval fails, or LLM generation fails
    """
    try:
        # Get project document metadata from MongoDB to find Main Document
        project_doc = await projects_collection.find_one({"project_id": project_manager.project_id})

        if not project_doc or "files_metadata" not in project_doc:
            raise Exception("No project document or files metadata found")

        # Find file with Main Document tag
        main_document_filename = None
        for file_metadata in project_doc.get("files_metadata", []):
            if DocumentTags.MAIN_DOCUMENT in file_metadata.get("tags", []):
                main_document_filename = file_metadata.get("filename")
                break

        if not main_document_filename:
            logger.warning("No file with Main Document tag found, returning empty proposal criteria")
            return {
                "content": [],
                "input_tokens": 0,
                "output_tokens": 0
            }

        logger.info(f"Found Main Document: {main_document_filename}")

        # Get the pickled content of the Main Document using the same logic as get_outline_generation_context()
        pickled_data = await project_manager.get_pickled_content(main_document_filename)
        if not pickled_data:
            raise Exception(f"No pickled content found for {main_document_filename}")

        logger.info(f"Reading pickled content for {main_document_filename}")

        # Unpickle the content and process using the same logic as outline_manager
        sections = pickle.loads(pickled_data)
        combined_content = []

        # Use the exact same logic as get_outline_generation_context()
        if isinstance(sections, list):
            for section in sections:
                if isinstance(section, dict):
                    # Handle different dictionary structures
                    if 'text' in section:
                        combined_content.append(section['text'])
                    elif 'content' in section:
                        combined_content.append(section['content'])
                    # Handle PDF page structure
                    elif 'page_number' in section and 'content' in section:
                        combined_content.append(f"Page {section['page_number']}: {section['content']}")
                elif isinstance(section, str):
                    combined_content.append(section)
        # If sections is a string, add it directly
        elif isinstance(sections, str):
            combined_content.append(sections)
        # Handle ProjectDocument object
        elif hasattr(sections, 'sections') and isinstance(sections.sections, list):
            for page in sections.sections:
                if isinstance(page, dict) and 'content' in page:
                    combined_content.append(page['content'])

        # Log the structure for debugging (same as outline_manager)
        logger.info(f"Sections type: {type(sections)}")
        if isinstance(sections, list):
            logger.info(f"Sections list length: {len(sections)}")
            if len(sections) > 0:
                logger.info(f"First section type: {type(sections[0])}")
                if isinstance(sections[0], dict):
                    logger.info(f"First section keys: {sections[0].keys()}")

        # Check if we have any valid content (same filtering as outline_manager)
        valid_content = []
        for content in combined_content:
            # Skip error messages from PDF extraction
            if not content.strip().startswith("Error: No content could be extracted from this page"):
                valid_content.append(content)

        if not valid_content:
            raise Exception("No content found in Main Document sections")

        # Join the content
        combined_content = "\n\n".join(valid_content)

        logger.info(f"Retrieved {len(combined_content)} characters of content from Main Document")

        # Get proposal criteria from rulebook
        proposal_criterias = get_content_review_rulebook("proposal_criterias")

        # Generate prompt using the RFP-specific review prompt
        prompt = get_rfp_specific_review_prompt(combined_content, proposal_criterias)

        # Configure LLM settings for content review generation (same as outline generation)
        generation_config = {
            "max_output_tokens": 8000,  # Allow for detailed criteria
        }

        # Initialize the LLM with project's model and configuration (same as outline generation)
        llm = get_llm_instance(
            model_name=project_manager.model_name,
            custom_config=generation_config
        )

        if llm is None:
            raise ValueError("LLM must be provided to generate proposal criteria")

        logger.info("Generating proposal-specific criteria...")
        logger.info("Model Name: " + project_manager.model_name)

        # Count input tokens
        input_tokens = count_tokens(prompt)
        logger.info(f"The input token count for the proposal criteria is: {input_tokens}")

        try:
            # Run the LLM operation in a thread pool to prevent blocking (same as outline generation)
            loop = asyncio.get_event_loop()

            # Import the required classes for type checking
            from langchain_google_genai import GoogleGenerativeAI

            if isinstance(llm, GoogleGenerativeAI):
                # For experimental models, invoke and get the response directly
                response = await loop.run_in_executor(
                    None,
                    lambda: llm.invoke(prompt)
                )
                output_tokens = count_tokens(response)
                logger.info(f"The output token count of the proposal criteria is: {output_tokens}")
                response_content = response
            else:
                # For standard models, invoke and get the structured response
                response = await loop.run_in_executor(
                    None,
                    lambda: llm.invoke(prompt, generation_config=generation_config)
                )
                output_tokens = count_tokens(response.content)
                logger.info(f"The output token count of the proposal criteria is: {output_tokens}")
                response_content = response.content

        except Exception as e:
            logger.error(f"Error during LLM generation: {str(e)}")
            raise Exception(f"Failed to generate proposal criteria: {str(e)}")

        logger.info(f"Generated proposal-specific criteria. Input tokens: {input_tokens}, Output tokens: {output_tokens}")

        # Clean and parse the response content to extract the JSON array
        try:
            # Use the existing clean_json_code_block function for consistency
            from utils import clean_json_code_block
            parsed_criteria = clean_json_code_block(response_content)

            # Ensure it's a list
            if not isinstance(parsed_criteria, list):
                logger.warning("LLM response is not a list, using raw content")
                parsed_criteria = response_content

            logger.info(f"Successfully parsed {len(parsed_criteria) if isinstance(parsed_criteria, list) else 'raw'} proposal criteria")

        except json.JSONDecodeError as e:
            logger.warning(f"Failed to parse LLM response as JSON: {str(e)}, using raw content")
            parsed_criteria = response_content
        except Exception as e:
            logger.warning(f"Error processing LLM response: {str(e)}, using raw content")
            parsed_criteria = response_content

        return {
            "content": parsed_criteria,
            "input_tokens": input_tokens,
            "output_tokens": output_tokens
        }

    except Exception as e:
        logger.error(f"Error generating proposal-specific criteria: {str(e)}")
        raise Exception(f"Error generating proposal-specific criteria: {str(e)}")


async def save_proposal_specific_criteria(project_manager, criteria_data: Dict[str, Any]) -> None:
    """Save proposal-specific criteria to GCS.

    Args:
        project_manager: ProjectManager instance
        criteria_data: Dictionary containing content, input_tokens, and output_tokens
    """
    try:
        # Construct the file path
        file_path = f"{project_manager.project_prefix}{StoragePaths.CONTENT_REVIEW_USER_SPECIFIC_FOLDER}{StoragePaths.GENERATED_PROPOSAL_SPECIFIC_EVALUATION_PARAMETERS_FILE}"

        # Get blob reference
        blob = project_manager.bucket.blob(file_path)

        # Save to GCS
        await asyncio.to_thread(blob.upload_from_string, json.dumps(criteria_data, indent=2))

        logger.info(f"Successfully saved proposal-specific criteria to: {file_path}")

    except Exception as e:
        logger.error(f"Error saving proposal-specific criteria: {str(e)}")
        raise Exception(f"Error saving proposal-specific criteria: {str(e)}")


async def get_existing_proposal_specific_criteria(project_manager) -> Optional[Dict[str, Any]]:
    """Retrieve existing proposal-specific criteria from GCS.

    Args:
        project_manager: ProjectManager instance

    Returns:
        Optional[Dict[str, Any]]: Existing criteria data or None if not found
    """
    try:
        # Construct the file path
        file_path = f"{project_manager.project_prefix}{StoragePaths.CONTENT_REVIEW_USER_SPECIFIC_FOLDER}{StoragePaths.GENERATED_PROPOSAL_SPECIFIC_EVALUATION_PARAMETERS_FILE}"

        # Get blob reference
        blob = project_manager.bucket.blob(file_path)

        # Check if file exists
        if not await asyncio.to_thread(blob.exists):
            return None

        # Download and parse content
        content = await asyncio.to_thread(blob.download_as_text)
        return json.loads(content)

    except NotFound:
        return None
    except Exception as e:
        logger.error(f"Error retrieving existing proposal-specific criteria: {str(e)}")
        return None


async def get_content_review_criteria(
    project_manager,
    criterias_type: Optional[str] = None,
    regenerate: bool = False
) -> Dict[str, Any]:
    """Get content review criteria with optional filtering and regeneration.

    Args:
        project_manager: ProjectManager instance
        criterias_type: Optional filter - "proposal_criterias", "general_criterias", or None for all
        regenerate: Whether to regenerate proposal-specific criteria

    Returns:
        Dict[str, Any]: Dictionary containing the requested criteria
    """
    try:
        result = {}

        # Handle general criteria (always from rulebook, never generated)
        if criterias_type is None or criterias_type == "general_criterias":
            general_criterias = get_content_review_rulebook("general_criterias")
            result["general_criterias"] = general_criterias

        # Handle proposal criteria (generated or retrieved)
        if criterias_type is None or criterias_type == "proposal_criterias":
            proposal_specific_data = None

            # Check if we need to regenerate or if no existing data
            if regenerate:
                # Get existing data to preserve token counts
                existing_data = await get_existing_proposal_specific_criteria(project_manager)

                # Generate new criteria (this will return empty list if no Main Document)
                new_criteria = await generate_proposal_specific_criteria(project_manager)

                # Only preserve and sum token counts if we have actual content and existing data
                if existing_data and new_criteria.get("content"):
                    new_criteria["input_tokens"] += existing_data.get("input_tokens", 0)
                    new_criteria["output_tokens"] += existing_data.get("output_tokens", 0)

                # Save the updated criteria (even if empty)
                await save_proposal_specific_criteria(project_manager, new_criteria)
                proposal_specific_data = new_criteria

                if new_criteria.get("content"):
                    logger.info("Regenerated and saved proposal-specific criteria")
                else:
                    logger.info("No Main Document found, saved empty proposal-specific criteria")
            else:
                # Try to get existing criteria first
                proposal_specific_data = await get_existing_proposal_specific_criteria(project_manager)

                # Check if we need to generate criteria:
                # 1. No existing data at all, OR
                # 2. Existing data is empty (no content) but Main Document is now available
                should_generate = False

                if not proposal_specific_data:
                    # No existing data at all
                    should_generate = True
                    logger.info("No existing proposal criteria found, will generate new ones")
                elif not proposal_specific_data.get("content"):
                    # Existing data is empty, check if Main Document is now available
                    project_doc = await projects_collection.find_one({"project_id": project_manager.project_id})
                    if project_doc and "files_metadata" in project_doc:
                        has_main_document = any(
                            DocumentTags.MAIN_DOCUMENT in file_metadata.get("tags", [])
                            for file_metadata in project_doc.get("files_metadata", [])
                        )
                        if has_main_document:
                            should_generate = True
                            logger.info("Found Main Document, will regenerate proposal criteria")
                        else:
                            logger.info("No Main Document available, keeping empty proposal criteria")
                    else:
                        logger.info("No project metadata found, keeping empty proposal criteria")
                else:
                    logger.info("Retrieved existing proposal-specific criteria with content")

                if should_generate:
                    proposal_specific_data = await generate_proposal_specific_criteria(project_manager)
                    await save_proposal_specific_criteria(project_manager, proposal_specific_data)
                    if proposal_specific_data.get("content"):
                        logger.info("Generated and saved new proposal-specific criteria")
                    else:
                        logger.info("No Main Document found, saved empty proposal-specific criteria")

            result["proposal_criterias"] = proposal_specific_data

        return result

    except Exception as e:
        logger.error(f"Error getting content review criteria: {str(e)}")
        raise Exception(f"Error getting content review criteria: {str(e)}")