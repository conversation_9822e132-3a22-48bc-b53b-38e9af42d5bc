"""Response Content Generation Module

This module handles the generation of response content from outlines using LLMs.
It provides functions for generating responses based on outlines and data library files.

Features:
- Response generation from system or user-uploaded outlines
- Data library integration for context-aware responses
- Section-based response structure
- Token usage tracking
- Comprehensive error handling with exception propagation
"""

import asyncio
import json
import logging
import re
from typing import List, Dict, Optional, Tuple, Any
import uuid
from datetime import datetime

from fastapi import H<PERSON><PERSON><PERSON>x<PERSON>
from google.cloud.exceptions import NotFound
from langchain_google_genai import GoogleGenerativeAI

from utils.llm_config import get_llm_instance
from utils.token_utils import count_tokens
from schemas import ResponseGenerationInput, ResponseContent, ResponseContentOutline
from utils.constants import HTTPStatus, StoragePaths, DocumentTags
from utils import clean_json_code_block, clean_text_content
from RAG.rfp_prompts import get_outline_breakdown_prompt, get_response_content_prompt

# Configure module logger
logger = logging.getLogger(__name__)

# System prompt for response generation
RESPONSE_GENERATION_SYSTEM_PROMPT = """You are a skilled content writer tasked with generating a comprehensive response based on the provided outline. Follow the structure of the outline exactly, expanding each section with detailed, relevant content. Ensure your writing is professional, clear, and thorough while addressing all points in the outline."""

def response_generation_prompt(outline_content: str, data_context: str = "") -> str:
    """Generate a prompt for response content generation.

    Args:
        outline_content: The outline to base the response on
        data_context: Optional additional context from data library files

    Returns:
        str: Formatted prompt for the LLM
    """
    prompt = f"""
Generate a comprehensive response based on the following outline:

{outline_content}

"""
    
    if data_context:
        prompt += f"""
Additional context and information:
{data_context}

"""
        
    prompt += """
Instructions:
1. Follow the exact structure of the outline provided
2. Expand each section with detailed, relevant content
3. Maintain a professional, clear writing style
4. Address all points mentioned in the outline thoroughly
5. Ensure consistency and logical flow between sections
6. Use concrete examples and specific details where appropriate
7. Keep the tone formal and suitable for business or technical contexts

Your response should be well-structured, informative, and comprehensive while following the outline exactly.
"""

    return prompt


async def get_past_performance_content(project_id: str) -> str:
    """Get content from files tagged with 'Past Performance'.

    Args:
        project_id: ID of the project

    Returns:
        str: Combined content from all past performance files
    """
    try:
        from project_manager import ProjectManager
        from utils.database import projects_collection

        # Load the project
        project = await ProjectManager.load_project(project_id)
        if not project:
            logger.warning(f"Project {project_id} not found for past performance content retrieval")
            return "No past performance content available."

        # Get all files with the "Past Performance" tag from MongoDB
        project_doc = await projects_collection.find_one({"project_id": project_id})
        combined_past_performance_content = []

        if project_doc and "files_metadata" in project_doc:
            for file_metadata in project_doc.get("files_metadata", []):
                if DocumentTags.PAST_PERFORMANCE in file_metadata.get("tags", []):
                    filename = file_metadata.get("filename", "Unknown File")

                    # Try to get the content from the pickled file
                    try:
                        # Get the pickled content
                        pickled_content = await project.get_pickled_content(filename)
                        if pickled_content:
                            import pickle
                            # Unpickle the content - this contains the sections list from processed_doc.sections
                            sections = pickle.loads(pickled_content)

                            # The pickled content is a list of sections from ProjectDocument.sections
                            if isinstance(sections, list) and sections:
                                # Extract content from each section and combine
                                file_content = "\n\n".join(
                                    section.get("content", "") if isinstance(section, dict)
                                    else str(section) for section in sections
                                )
                                if file_content.strip():  # Only add non-empty content
                                    combined_past_performance_content.append(file_content)
                                logger.info(f"Retrieved content from {len(sections)} sections for past performance file: {filename}")
                            else:
                                logger.warning(f"Unexpected pickled content structure for past performance file: {filename}")
                    except Exception as e:
                        logger.error(f"Error getting pickled content for past performance file {filename}: {str(e)}")

        # Combine all past performance content
        if combined_past_performance_content:
            final_content = "\n\n".join(combined_past_performance_content)
            logger.info(f" Successfully combined content from {len(combined_past_performance_content)} past performance files")
            return final_content
        else:
            logger.warning(f"  No past performance files found for project {project_id}. Files with 'Past Performance' tag are required for optimal response generation.")
            return "No past performance content available."

    except Exception as e:
        logger.error(f" Error retrieving past performance content for project {project_id}: {str(e)}")
        return "Error retrieving past performance content."


async def get_summary_content(project_id: str) -> str:
    """Get summary content for specific sections like Proposal Info, Scope and Background.

    Args:
        project_id: ID of the project

    Returns:
        str: Combined summary content from specified sections
    """
    try:
        from project_manager import ProjectManager
        from utils.constants import Sections

        # Load the project
        project = await ProjectManager.load_project(project_id)
        if not project:
            logger.warning(f"Project {project_id} not found for summary content retrieval")
            return "No summary content available."

        # Get the summaries from storage
        summaries_blob = project.bucket.blob(f"{project.project_prefix}{StoragePaths.SUMMARIES_FILE}")

        try:
            loop = asyncio.get_event_loop()
            summaries_text = await loop.run_in_executor(None, summaries_blob.download_as_text)
            summaries_data = json.loads(summaries_text)

            # Extract content from specific sections
            target_sections = [
                Sections.PROPOSAL_INFO,
                Sections.SCOPE,
                Sections.BACKGROUND
            ]

            summary_content_parts = []

            for section_name in target_sections:
                if section_name in summaries_data:
                    section_data = summaries_data[section_name]

                    # Get the content from the section (without adding extra headers)
                    if isinstance(section_data, dict):
                        content = section_data.get("content", "")
                        if content and content.strip():
                            summary_content_parts.append(content)
                            logger.info(f" Retrieved summary content for section: {section_name}")
                        else:
                            logger.warning(f"  Section '{section_name}' exists but has empty content in project {project_id}")
                    elif isinstance(section_data, str):
                        if section_data.strip():
                            summary_content_parts.append(section_data)
                            logger.info(f" Retrieved summary content for section: {section_name}")
                        else:
                            logger.warning(f"  Section '{section_name}' exists but has empty content in project {project_id}")
                else:
                    logger.warning(f"  Section '{section_name}' not found in summaries for project {project_id}")

            # Combine all summary content
            if summary_content_parts:
                final_summary_content = "\n\n".join(summary_content_parts)
                logger.info(f" Successfully combined summary content from {len(summary_content_parts)} sections: {', '.join([s for s in target_sections if s in summaries_data])}")
                return final_summary_content
            else:
                available_sections = list(summaries_data.keys()) if summaries_data else []
                logger.warning(f"  No summary content found for target sections (Proposal Info, Scope, Background) in project {project_id}. Available sections: {available_sections}")
                return "No summary content available for Proposal Info, Scope, and Background sections."

        except NotFound:
            logger.warning(f"  No summaries.json file found for project {project_id}. Summary content is required for optimal response generation.")
            return "No summaries available for this project."

    except Exception as e:
        logger.error(f" Error retrieving summary content for project {project_id}: {str(e)}")
        return "Error retrieving summary content."


async def process_single_outline_content(
    outline_content: str,
    past_performance_content: str,
    summary_content: str,
    llm,
    generation_config: dict,
    file_identifier: str
) -> dict:
    """Process a single outline content through breakdown and section-by-section generation.

    Args:
        outline_content: The outline content to process
        past_performance_content: Past performance content
        summary_content: Summary content
        llm: The LLM instance
        generation_config: LLM configuration
        file_identifier: Identifier for logging purposes

    Returns:
        dict: Contains 'content', 'input_tokens', 'output_tokens'
    """
    logger.info(f"Processing outline content for {file_identifier}")

    # Step 1: Get the outline breakdown prompt
    breakdown_prompt = get_outline_breakdown_prompt(outline_content)
    breakdown_input_tokens = count_tokens(breakdown_prompt)

    # Step 2: Invoke the model with the breakdown prompt
    loop = asyncio.get_event_loop()

    if isinstance(llm, GoogleGenerativeAI):
        # For experimental models, invoke and get the response directly
        outline_breakdown = await loop.run_in_executor(
            None,
            lambda: llm.invoke(breakdown_prompt)
        )
        breakdown_content = outline_breakdown
    else:
        # For standard models, invoke and get the structured response
        outline_breakdown = await loop.run_in_executor(
            None,
            lambda: llm.invoke(breakdown_prompt, generation_config=generation_config)
        )
        breakdown_content = outline_breakdown.content

    breakdown_output_tokens = count_tokens(breakdown_content)

    # Step 3: Clean the JSON code block and parse it
    try:
        jsonified_outline = clean_json_code_block(breakdown_content)
        logger.info(f"Successfully parsed outline breakdown JSON with {len(jsonified_outline.get('sections', []))} sections for {file_identifier}")
    except (ValueError, json.JSONDecodeError) as e:
        logger.error(f"Failed to parse JSON from outline breakdown for {file_identifier}: {str(e)}")
        # Fallback to original outline content
        jsonified_outline = {
            "sections": [
                {
                    "section_number": "1",
                    "section_title": "Main Content",
                    "subsections": [
                        {
                            "subsection_title": "Content",
                            "content": outline_content
                        }
                    ]
                }
            ]
        }

    # Step 4: Process each section individually
    combined_response = ""
    total_section_input_tokens = 0
    total_section_output_tokens = 0

    # Loop through each section in the sections array
    for section in jsonified_outline["sections"]:
        section_number = section["section_number"]
        section_title = section["section_title"]

        logger.info(f"====================Processing section {section_number} for {file_identifier}================")

        # Generate the prompt for this section, including all its subsection titles
        prompt = get_response_content_prompt(
            full_outline=outline_content,
            current_section_outline=section_title,
            past_performance=past_performance_content,
            procurement_summary=summary_content
        )

        section_input_tokens = count_tokens(prompt)
        total_section_input_tokens += section_input_tokens

        # Invoke the model with the generated prompt
        if isinstance(llm, GoogleGenerativeAI):
            response = await loop.run_in_executor(
                None,
                lambda p=prompt: llm.invoke(p)
            )
            response_content = response
        else:
            response = await loop.run_in_executor(
                None,
                lambda p=prompt: llm.invoke(p, generation_config=generation_config)
            )
            response_content = response.content

        logger.info("+++++++++++++++ACTUAL LLM RESPONSE CONTENT++++++++++++++")
        logger.info(response_content[:100])
        logger.info("+++++++++++++++END LLM RESPONSE CONTENT++++++++++++++")

        section_output_tokens = count_tokens(response_content)
        total_section_output_tokens += section_output_tokens
        # Clean the markdown code block from the response
        cleaned_response = clean_text_content(response_content)

        logger.info(f"-------------------------RESPONSE for {section_number}:{section_title} ({file_identifier})---------------")
        logger.info(f"Response preview: {cleaned_response[:100]}...")
        logger.info(f"-------------------------END RESPONSE for {section_number}:{section_title} ({file_identifier})---------------")

        

        # Append the cleaned response to the combined string
        combined_response += cleaned_response + "\n\n"

        logger.info(f"====================== END Section {section_number} for {file_identifier}==================================")

    # Calculate total tokens for this outline
    total_input_tokens = breakdown_input_tokens + total_section_input_tokens
    total_output_tokens = breakdown_output_tokens + total_section_output_tokens

    logger.info(f"Successfully processed {file_identifier} with {total_output_tokens} total output tokens")

    return {
        'content': combined_response.strip(),
        'input_tokens': total_input_tokens,
        'output_tokens': total_output_tokens
    }


async def generate_response_content_with_breakdown(input_data: ResponseGenerationInput) -> ResponseContent:
    """Generate response content using outline breakdown approach with section-by-section processing.

    This function implements different logic based on outline_type:
    - For system_generated/custom_outline: Process single outline
    - For user_uploaded: Process each outline-tagged file individually, then combine results

    Args:
        input_data: Input parameters for response generation

    Returns:
        ResponseContent: Generated response with metadata

    Raises:
        ValueError: If outline content is empty or invalid
        HTTPException: If response generation fails
    """
    if input_data.outline_type == "user_uploaded":
        # Special handling for user_uploaded: process each outline file individually
        return await generate_response_from_multiple_outline_files(input_data)
    else:
        # Standard processing for system_generated and custom_outline
        return await generate_response_from_single_outline(input_data)


async def generate_response_from_multiple_outline_files(input_data: ResponseGenerationInput) -> ResponseContent:
    """Generate response content by processing each outline-tagged file individually.

    For user_uploaded outline type:
    1. Get all files tagged with "Outline"
    2. For each file: do outline breakdown → generate response content for each section
    3. Combine all generated responses from all files
    4. Return final combined response content

    Args:
        input_data: Input parameters for response generation

    Returns:
        ResponseContent: Generated response with metadata from all outline files
    """
    from project_manager import ProjectManager
    from utils.database import projects_collection

    # Configure LLM settings for response generation
    generation_config = {
        "temperature": 0.2
    }

    # Initialize the LLM with specified model and configuration
    llm = get_llm_instance(
        model_name=input_data.model_name,
        custom_config=generation_config
    )

    if llm is None:
        raise ValueError("LLM must be provided to generate response")

    logger.info(f"Generating response content from multiple outline files for project: {input_data.project_id}")

    try:
        # Load the project
        project = await ProjectManager.load_project(input_data.project_id)
        if not project:
            raise ValueError(f"Project {input_data.project_id} not found")

        # Get all files with the "Outline" tag from MongoDB
        project_doc = await projects_collection.find_one({"project_id": input_data.project_id})

        if not project_doc or "files_metadata" not in project_doc:
            raise ValueError("No files found in project")

        outline_files = []
        for file_metadata in project_doc.get("files_metadata", []):
            if DocumentTags.OUTLINE in file_metadata.get("tags", []):
                outline_files.append(file_metadata)

        if not outline_files:
            from utils.constants import HTTPStatus
            from fastapi import HTTPException
            raise HTTPException(
                status_code=HTTPStatus.NOT_FOUND,
                detail="No file available with outline tag."
            )

        logger.info(f"Found {len(outline_files)} outline-tagged files to process")

        # Get past performance content once (shared across all files)
        past_performance_content = await get_past_performance_content(input_data.project_id)
        summary_content = await get_summary_content(input_data.project_id)

        # Process each outline file individually
        all_file_responses = []
        total_input_tokens = 0
        total_output_tokens = 0

        for file_index, file_metadata in enumerate(outline_files, 1):
            filename = file_metadata.get("filename", f"Outline_File_{file_index}")
            logger.info(f"==================== Processing Outline File {file_index}/{len(outline_files)}: {filename} ====================")

            # Get content from this specific outline file
            try:
                pickled_content = await project.get_pickled_content(filename)
                if not pickled_content:
                    logger.warning(f"No content found for outline file: {filename}")
                    continue

                import pickle
                sections = pickle.loads(pickled_content)

                if isinstance(sections, list) and sections:
                    # Extract content from each section and combine for this file
                    file_outline_content = "\n\n".join(
                        section.get("content", "") if isinstance(section, dict)
                        else str(section) for section in sections
                    )

                    if not file_outline_content.strip():
                        logger.warning(f"Empty content for outline file: {filename}")
                        continue

                    logger.info(f"Retrieved content from {len(sections)} sections for outline file: {filename}")

                    # Process this individual file's outline
                    file_response = await process_single_outline_content(
                        outline_content=file_outline_content,
                        past_performance_content=past_performance_content,
                        summary_content=summary_content,
                        llm=llm,
                        generation_config=generation_config,
                        file_identifier=f"File_{file_index}_{filename}"
                    )

                    all_file_responses.append(f"## Response from {filename}\n\n{file_response['content']}")
                    total_input_tokens += file_response['input_tokens']
                    total_output_tokens += file_response['output_tokens']

                else:
                    logger.warning(f"Unexpected content structure for outline file: {filename}")

            except Exception as e:
                logger.error(f"Error processing outline file {filename}: {str(e)}")
                continue

            logger.info(f"==================== Completed Processing File {file_index}/{len(outline_files)}: {filename} ====================")

        # Combine all file responses
        if all_file_responses:
            final_combined_response = "\n\n".join(all_file_responses)
            logger.info(f"Successfully combined responses from {len(all_file_responses)} outline files")

            # Clean the combined response to ensure consistency
            cleaned_combined_response = clean_text_content(final_combined_response, mode='markdown')
        else:
            from utils.constants import HTTPStatus
            from fastapi import HTTPException
            raise HTTPException(
                status_code=HTTPStatus.NOT_FOUND,
                detail="No valid content found in any outline-tagged files. Please ensure outline files contain valid content."
            )

        # Create the response object
        return ResponseContent(
            content=cleaned_combined_response,
            outline_type=input_data.outline_type,
            data_library_files=input_data.data_library_files,
            input_tokens=total_input_tokens,
            output_tokens=total_output_tokens
        )

    except Exception as e:
        logger.error(f"Error generating response from multiple outline files: {str(e)}")
        raise


async def generate_response_from_single_outline(input_data: ResponseGenerationInput) -> ResponseContent:
    """Generate response content from a single outline (system_generated or custom_outline).

    Args:
        input_data: Input parameters for response generation

    Returns:
        ResponseContent: Generated response with metadata
    """
    outline_content = input_data.outline_content

    if not outline_content:
        raise ValueError("Outline content cannot be empty")

    # Configure LLM settings for response generation
    generation_config = {
        "temperature": 0.2
    }

    # Initialize the LLM with specified model and configuration
    llm = get_llm_instance(
        model_name=input_data.model_name,
        custom_config=generation_config
    )

    if llm is None:
        raise ValueError("LLM must be provided to generate response")

    logger.info(f"Generating response content using outline breakdown for type: {input_data.outline_type}")

    try:
        # Get past performance content
        past_performance_content = await get_past_performance_content(input_data.project_id)
        summary_content = await get_summary_content(input_data.project_id)

        # Process the single outline
        response_data = await process_single_outline_content(
            outline_content=outline_content,
            past_performance_content=past_performance_content,
            summary_content=summary_content,
            llm=llm,
            generation_config=generation_config,
            file_identifier="single_outline"
        )

        # Create the response object
        return ResponseContent(
            content=response_data['content'],
            outline_type=input_data.outline_type,
            data_library_files=input_data.data_library_files,
            input_tokens=response_data['input_tokens'],
            output_tokens=response_data['output_tokens']
        )

    except Exception as e:
        logger.error(f"Error generating response content: {str(e)}")
        raise HTTPException(
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate response content: {str(e)}"
        )


def format_json_outline_to_response(jsonified_outline: Dict[str, Any]) -> str:
    """Convert the JSON outline structure to a readable response format.

    Args:
        jsonified_outline: The parsed JSON outline structure

    Returns:
        str: Formatted response content
    """
    response_parts = ["# Response Content"]

    sections = jsonified_outline.get("sections", [])

    for section in sections:
        section_title = section.get("section_title", "Untitled Section")
        response_parts.append(f"\n## {section_title}")

        subsections = section.get("subsections", [])
        for subsection in subsections:
            subsection_title = subsection.get("subsection_title", "Untitled Subsection")
            content = subsection.get("content", "No content available")

            response_parts.append(f"\n### {subsection_title}")
            response_parts.append(f"\n{content}")

    return "\n".join(response_parts)


async def generate_response_from_outline(input_data: ResponseGenerationInput) -> ResponseContent:
    """Generate a response based on an outline using an LLM.

    This function processes the outline and generates a structured response.
    It uses the specified LLM model and handles both experimental and standard models.

    Args:
        input_data: Input parameters for response generation

    Returns:
        ResponseContent: Generated response with metadata

    Raises:
        ValueError: If LLM initialization fails
        HTTPException: If response generation fails
    """
    outline_content = input_data.outline_content
    
    if not outline_content:
        raise ValueError("Outline content cannot be empty")
    
    # Configure LLM settings for response generation
    generation_config = {
        "temperature": 0.2
    }

    # Initialize the LLM with specified model and configuration
    llm = get_llm_instance(
        model_name=input_data.model_name,
        custom_config=generation_config
    )

    if llm is None:
        raise ValueError("LLM must be provided to generate response")

    logger.info(f"Generating response using outline: {input_data.outline_name}")
    
    # For now, we'll use a dummy data context
    # In a real implementation, this would process the data library files
    data_context = ""
    if input_data.data_library_files:
        data_context = f"Additional context from {len(input_data.data_library_files)} data library files."
    
    # Prepare sections based on the outline
    response_sections = extract_sections_from_outline(outline_content)
    
    # Generate the response prompt
    response_prompt = response_generation_prompt(outline_content, data_context)
    input_tokens = count_tokens(response_prompt)
    
    try:
        # Run the LLM operation in a thread pool to prevent blocking
        loop = asyncio.get_event_loop()
        
        if isinstance(llm, GoogleGenerativeAI):
            # For experimental models, invoke and get the response directly
            response = await loop.run_in_executor(
                None,
                lambda: llm.invoke(response_prompt)
            )
            generated_response = response
            output_tokens = count_tokens(generated_response)
        else:
            # For standard models, invoke and get the structured response
            response = await loop.run_in_executor(
                None,
                lambda: llm.invoke(response_prompt, generation_config=generation_config)
            )
            generated_response = response.content
            output_tokens = count_tokens(generated_response)
        
        logger.info(f"Successfully generated response with {output_tokens} tokens")

        # Clean the generated response before returning
        cleaned_response = clean_text_content(generated_response, mode='markdown')

        # Create the response object
        return ResponseContent(
            content=cleaned_response,
            outline_type=input_data.outline_type,
            data_library_files=input_data.data_library_files,
            input_tokens=input_tokens,
            output_tokens=output_tokens
        )
        
    except Exception as e:
        logger.error(f"Error generating response: {str(e)}")
        
        raise HTTPException(
            status_code=500,
            detail=f"Failed to generate response: {str(e)}"
        )


def extract_sections_from_outline(outline_content: str) -> List[Dict[str, Any]]:
    """Extract sections and subsections from an outline.

    Args:
        outline_content: The outline text to parse

    Returns:
        List[Dict[str, Any]]: List of section objects with title and subsections
    """
    sections = []
    
    # Extract sections using regex
    section_pattern = r'#+\s+(.*?)(?=\n#+\s+|\Z)'
    matches = re.findall(section_pattern, outline_content, re.DOTALL)
    
    for match in matches:
        lines = match.strip().split('\n')
        if lines:
            title = lines[0].strip()
            content = '\n'.join(lines[1:]) if len(lines) > 1 else ""
            sections.append({
                "title": title,
                "content": content
            })
    
    # If no sections found, create a default section
    if not sections:
        sections.append({
            "title": "Main Content",
            "content": outline_content
        })
    
    return sections







async def get_available_outlines(project_id: str, project, projects_collection) -> List[ResponseContentOutline]:
    """Get all available outlines for a project.

    Args:
        project_id: ID of the project
        project: ProjectManager instance
        projects_collection: MongoDB collection for projects

    Returns:
        List[ResponseContentOutline]: List of available outlines

    Raises:
        ValueError: If project is invalid
    """
    if not project:
        raise ValueError(f"Project not found: {project_id}")
    
    outlines = []
    loop = asyncio.get_event_loop()

    # Get system-generated outline if it exists
    outline_blob = project.bucket.blob(f"{project.project_prefix}{StoragePaths.OUTLINE_FILE}")
    try:
        # Try to load existing outline from storage
        outline_text = await loop.run_in_executor(None, outline_blob.download_as_text)
        outline_data = json.loads(outline_text)
        from utils import strip_markdown_delimiters
        outline = strip_markdown_delimiters(outline_data.get("content", outline_data.get("outline", "")))  # Support both new and old key for backward compatibility

        # Only add system outline if it has content
        if outline and outline.strip():
            outlines.append(ResponseContentOutline(
                name="System Generated Outline",
                type="system_generated",
                content=outline
            ))
            logger.info(f"Retrieved system outline for project {project_id}")
        else:
            logger.info(f"System outline exists but is empty for project {project_id}")
    except NotFound:
        logger.info(f"No system-generated outline found for project {project_id}")
        # Don't add empty system outline entry



    # Get user-uploaded outlines
    # Get all files with the "Outline" tag from MongoDB directly and combine their content
    project_doc = await projects_collection.find_one({"project_id": project_id})
    combined_outline_content = []
    outline_files_processed = 0

    if project_doc and "files_metadata" in project_doc:
        for file_metadata in project_doc.get("files_metadata", []):
            if DocumentTags.OUTLINE in file_metadata.get("tags", []):
                filename = file_metadata.get("filename", "Unknown File")

                # Try to get the content from the pickled file
                try:
                    # Get the pickled content
                    pickled_content = await project.get_pickled_content(filename)
                    if pickled_content:
                        import pickle
                        # Unpickle the content - this contains the sections list from processed_doc.sections
                        sections = pickle.loads(pickled_content)

                        # The pickled content is a list of sections from ProjectDocument.sections
                        if isinstance(sections, list) and sections:
                            # Extract content from each section and combine
                            file_content = "\n\n".join(
                                section.get("content", "") if isinstance(section, dict)
                                else str(section) for section in sections
                            )
                            if file_content.strip():  # Only add non-empty content
                                combined_outline_content.append(file_content)
                                outline_files_processed += 1
                            logger.info(f"Retrieved content from {len(sections)} sections for outline file: {filename}")
                        else:
                            logger.warning(f"Unexpected pickled content structure for file: {filename}")
                            logger.warning(f"Content type: {type(sections)}, Content: {sections}")
                except Exception as e:
                    logger.error(f"Error getting pickled content for outline file {filename}: {str(e)}")

    # Create a single user-uploaded outline entry with combined content only if files exist
    if outline_files_processed > 0:
        final_combined_content = "\n\n".join(combined_outline_content)
        logger.info(f"Combined content from {outline_files_processed} outline files into single user-uploaded outline")
        outlines.append(ResponseContentOutline(
            name="User Uploaded Outline",
            type="user_uploaded",
            content=final_combined_content
        ))
    else:
        # Don't add empty user-uploaded outline entry if no outline files found
        logger.info("No user-uploaded outline files found, not adding to available outlines")

    return outlines