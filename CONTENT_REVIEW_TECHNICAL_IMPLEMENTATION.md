# Content Review Pipeline - Technical Implementation

## Overview

This document provides a technical implementation guide for the ProposalPro content review pipeline. The system evaluates user-provided content against two types of criteria: General Criteria (from a static rulebook) and Proposal-Specific Criteria (dynamically generated from the Main Document). The pipeline uses LLM-based evaluation to provide structured feedback, scores, and improvement suggestions.

## Architecture Flow

1. **Criteria Generation/Retrieval** → Load general criteria and generate/retrieve proposal-specific criteria
2. **Content Review Request** → User submits content with selected criteria for evaluation
3. **LLM-Based Evaluation** → Each criterion is applied individually to the content
4. **Structured Response Generation** → JSON-formatted results with scores and feedback
5. **Results Storage** → Save review results to GCS with cumulative token tracking

## Stage 1: Content Review Criteria Generation and Retrieval

### Criteria Types and Sources

The content review system operates with two distinct types of evaluation criteria:

**General Criteria (Static)**:
- **Source**: Static rulebook stored in GCS at `CONTENT_REVIEW_RULEBOOK/content_review_rulebook.json`
- **Content**: Universal evaluation rules applicable to any proposal content
- **Structure**: JSON array with `name`, `type`, and `check` fields
- **Token Cost**: No tokens required (static retrieval)

**Proposal-Specific Criteria (Dynamic)**:
- **Source**: Generated from Main Document content using LLM
- **Content**: Customized evaluation rules specific to the RFP requirements
- **Generation Process**: Uses Main Document content + general criteria template
- **Token Cost**: Tracked and accumulated across generations

### Technical Implementation - Criteria Retrieval

**Function**: `get_content_review_criteria()`
**Process**: Intelligent criteria management with caching and regeneration logic

```python
# Decision flow for proposal criteria:
if regenerate_requested:
    generate_new_criteria()
    preserve_existing_token_counts()
    save_updated_criteria()
elif no_existing_criteria:
    generate_initial_criteria()
    save_new_criteria()
elif existing_criteria_empty and main_document_available:
    regenerate_criteria()
    save_updated_criteria()
else:
    return_cached_criteria()
```

### Proposal-Specific Criteria Generation Process

**Input Requirements**:
- Main Document content (tagged with `DocumentTags.MAIN_DOCUMENT`)
- General criteria template from rulebook
- Project's configured LLM model

**Content Processing Pipeline**:
1. **Document Retrieval**: Load pickled Main Document content from GCS
2. **Content Extraction**: Parse document sections using same logic as outline generation
3. **Content Validation**: Filter out extraction errors and empty sections
4. **Prompt Generation**: Combine content with general criteria using RFP-specific prompt
5. **LLM Processing**: Generate customized criteria with 8000 token limit
6. **Response Parsing**: Extract and validate JSON array structure
7. **Storage**: Save to project-specific path with token metadata

## Stage 2: Content Review Request Processing

### Request Structure and Validation

**Input Components**:
- `content_to_be_reviewed`: Text content for evaluation
- `criteria`: Array of evaluation rules (from criteria endpoint)

**Validation Process**:
- Project existence and user access verification
- Criteria format validation (name, type, check fields)
- Content length and format validation
- LLM model availability confirmation

### Technical Implementation - Request Flow

**Route**: `POST /projects/{project_id}/review-content`
**Process**: Sequential evaluation of each criterion against content

```python
# Processing flow:
for each_criterion in criteria:
    generate_evaluation_prompt(content, criterion)
    count_input_tokens(prompt)
    invoke_llm(prompt)
    count_output_tokens(response)
    parse_json_response(response)
    accumulate_results()
```

## Stage 3: LLM-Based Content Evaluation

### Evaluation Prompt Structure

**Prompt Components**:
- Professional reviewer role definition
- Single-criterion evaluation instruction
- Strict JSON output format specification
- Evaluation rule details
- Content to be reviewed

**Output Format Requirements**:
```json
{
  "rule_name": "Criterion name",
  "rule_type": "Criterion category",
  "score": "X/10 or N/A",
  "feedback": "Detailed evaluation observation",
  "suggestions": "Improvement recommendations or N/A"
}
```

### LLM Processing Implementation

**Model Support**:
- Google Generative AI models (experimental)
- Standard Vertex AI models
- Project-specific model configuration

**Token Management**:
- Input token counting per criterion
- Output token counting per response
- Cumulative tracking across all evaluations
- Preservation of historical token counts

**Error Handling**:
- JSON parsing failure recovery
- Individual criterion failure isolation
- Partial result preservation
- Detailed error logging

## Stage 4: Results Processing and Storage

### Response Structure Generation

**Individual Result Format**:
- `rule_name`: Applied criterion name
- `rule_type`: Criterion category
- `score`: Numerical or qualitative score
- `feedback`: Detailed evaluation feedback
- `suggestions`: Improvement recommendations

**Aggregated Response Format**:
- `content`: Array of individual results
- `input_tokens`: Cumulative input token count
- `output_tokens`: Cumulative output token count

### Storage Implementation

**Storage Location**: `{project_prefix}content_review/generated_content_review.json`

**Data Persistence**:
- Review results without original content
- Cumulative token counts (current + historical)
- Timestamp metadata through project manager
- JSON format with proper indentation

**Token Accumulation Logic**:
```python
# Token preservation across reviews:
previous_tokens = load_existing_review_data()
current_tokens = calculate_current_review_tokens()
accumulated_tokens = previous_tokens + current_tokens
save_accumulated_totals(accumulated_tokens)
```

## Stage 5: Results Retrieval

### GET Endpoint Implementation

**Route**: `GET /projects/{project_id}/content-review`
**Purpose**: Retrieve previously generated content review results

**Response Processing**:
- Load stored review data from GCS
- Remove sensitive content fields for security
- Maintain backward compatibility
- Update project visit timestamps

**Error Handling**:
- Missing review data detection
- Graceful degradation for incomplete data
- User-friendly error messages
- Proper HTTP status codes

## Key Technical Features

### Intelligent Criteria Management
- **Automatic Generation**: Creates proposal-specific criteria when Main Document is available
- **Caching Strategy**: Preserves generated criteria to avoid unnecessary regeneration
- **Smart Regeneration**: Detects when new Main Document requires criteria update
- **Token Preservation**: Maintains cumulative token counts across operations

### Robust Error Handling
- **Partial Failure Recovery**: Continues evaluation even if individual criteria fail
- **JSON Parsing Resilience**: Handles malformed LLM responses gracefully
- **Storage Failure Tolerance**: Continues operation even if save operations fail
- **Detailed Logging**: Comprehensive error tracking and debugging information

### Performance Optimization
- **Asynchronous Processing**: Non-blocking LLM operations using thread pools
- **Efficient Storage**: Minimal data persistence without content duplication
- **Token Efficiency**: Precise token counting and accumulation
- **Caching Strategy**: Intelligent criteria reuse to minimize LLM calls

### Security and Access Control
- **User Authentication**: Integrated with project-based access control
- **Content Sanitization**: Removes sensitive content from stored results
- **Project Isolation**: Strict project-based data separation
- **Audit Trail**: Comprehensive logging for security monitoring

## Configuration and Dependencies

### Required Components
- **Google Cloud Storage**: For criteria rulebook and results storage
- **MongoDB**: For project metadata and document tracking
- **LLM Integration**: Vertex AI or Google Generative AI models
- **Authentication System**: User access control and project permissions

### Storage Paths
- **Rulebook**: `CONTENT_REVIEW_RULEBOOK/content_review_rulebook.json`
- **Project Criteria**: `{project_prefix}content_review/generated_proposal_specific_evaluation_parameters.json`
- **Review Results**: `{project_prefix}content_review/generated_content_review.json`

### Token Management
- **Input Tracking**: Per-prompt token counting
- **Output Tracking**: Per-response token counting
- **Cumulative Storage**: Historical token preservation
- **Cost Monitoring**: Detailed usage analytics

## Implementation Examples

### Example 1: Criteria Generation Flow

```python
# Generate proposal-specific criteria
async def generate_proposal_specific_criteria(project_manager):
    # 1. Find Main Document
    project_doc = await projects_collection.find_one({"project_id": project_manager.project_id})
    main_document_filename = find_main_document(project_doc)

    # 2. Load and process content
    pickled_data = await project_manager.get_pickled_content(main_document_filename)
    sections = pickle.loads(pickled_data)
    combined_content = process_document_sections(sections)

    # 3. Generate criteria using LLM
    proposal_criterias = get_content_review_rulebook("proposal_criterias")
    prompt = get_rfp_specific_review_prompt(combined_content, proposal_criterias)

    # 4. LLM processing with token tracking
    llm = get_llm_instance(model_name=project_manager.model_name)
    input_tokens = count_tokens(prompt)
    response = await llm.invoke(prompt)
    output_tokens = count_tokens(response)

    # 5. Parse and return structured result
    parsed_criteria = clean_json_code_block(response)
    return {
        "content": parsed_criteria,
        "input_tokens": input_tokens,
        "output_tokens": output_tokens
    }
```

### Example 2: Content Review Evaluation

```python
# Review content against multiple criteria
async def review_content(project_id, content, criteria):
    # 1. Initialize tracking
    review_results = []
    total_input_tokens = 0
    total_output_tokens = 0

    # 2. Process each criterion individually
    for rule in criteria:
        # Generate evaluation prompt
        prompt = get_review_prompt(content, rule)
        input_tokens = count_tokens(prompt)

        # Get LLM evaluation
        response = await llm.invoke(prompt)
        output_tokens = count_tokens(response)

        # Parse structured response
        json_result = parse_json_proposal_info(response)
        review_results.append(json_result)

        # Accumulate tokens
        total_input_tokens += input_tokens
        total_output_tokens += output_tokens

    # 3. Combine with historical data
    previous_data = load_existing_review_data()
    accumulated_input = total_input_tokens + previous_data.get("input_tokens", 0)
    accumulated_output = total_output_tokens + previous_data.get("output_tokens", 0)

    # 4. Return structured results
    return {
        "content": review_results,
        "input_tokens": accumulated_input,
        "output_tokens": accumulated_output
    }
```

### Example 3: Criteria Retrieval Logic

```python
# Intelligent criteria management
async def get_content_review_criteria(project_manager, criterias_type=None, regenerate=False):
    result = {}

    # Handle general criteria (always from rulebook)
    if criterias_type is None or criterias_type == "general_criterias":
        general_criterias = get_content_review_rulebook("general_criterias")
        result["general_criterias"] = {
            "content": general_criterias,
            "input_tokens": 0,
            "output_tokens": 0
        }

    # Handle proposal criteria (generated or cached)
    if criterias_type is None or criterias_type == "proposal_criterias":
        if regenerate:
            # Force regeneration
            new_criteria = await generate_proposal_specific_criteria(project_manager)
            existing_data = await get_existing_proposal_specific_criteria(project_manager)

            # Preserve token history
            if existing_data:
                new_criteria["input_tokens"] += existing_data.get("input_tokens", 0)
                new_criteria["output_tokens"] += existing_data.get("output_tokens", 0)

            await save_proposal_specific_criteria(project_manager, new_criteria)
            result["proposal_criterias"] = new_criteria
        else:
            # Try to use existing, generate if needed
            existing_criteria = await get_existing_proposal_specific_criteria(project_manager)

            if not existing_criteria or not existing_criteria.get("content"):
                # Generate new criteria if none exist or empty
                if has_main_document(project_manager):
                    new_criteria = await generate_proposal_specific_criteria(project_manager)
                    await save_proposal_specific_criteria(project_manager, new_criteria)
                    result["proposal_criterias"] = new_criteria
                else:
                    # No Main Document available
                    result["proposal_criterias"] = {
                        "content": [],
                        "input_tokens": 0,
                        "output_tokens": 0
                    }
            else:
                # Use existing criteria
                result["proposal_criterias"] = existing_criteria

    return result
```

## API Endpoints Summary

### GET /projects/{project_id}/content-review-criteria
**Purpose**: Retrieve evaluation criteria for content review
**Parameters**:
- `criterias_type`: Optional filter ("general_criterias" or "proposal_criterias")
**Response**: Criteria data with token metadata
**Key Features**:
- Automatic proposal criteria generation
- Intelligent caching and regeneration
- Token cost tracking

### POST /projects/{project_id}/review-content
**Purpose**: Evaluate content against provided criteria
**Request Body**:
- `content_to_be_reviewed`: Text content for evaluation
- `criteria`: Array of evaluation rules
**Response**: Structured review results with scores and feedback
**Key Features**:
- Individual criterion evaluation
- Cumulative token tracking
- Partial failure recovery

### GET /projects/{project_id}/content-review
**Purpose**: Retrieve previously generated content review results
**Response**: Stored review results with cumulative token counts
**Key Features**:
- Historical data retrieval
- Content sanitization
- Backward compatibility

## Error Handling and Edge Cases

### Missing Main Document
- **Scenario**: No file tagged with `DocumentTags.MAIN_DOCUMENT`
- **Behavior**: Return empty proposal criteria with zero token cost
- **User Impact**: Only general criteria available for review

### LLM Response Parsing Failures
- **Scenario**: Malformed JSON in LLM response
- **Behavior**: Skip individual criterion, continue with others
- **Logging**: Detailed error information for debugging
- **User Impact**: Partial results returned

### Storage Operation Failures
- **Scenario**: GCS write operations fail
- **Behavior**: Continue execution, log errors
- **User Impact**: Results still returned to user
- **Recovery**: Manual retry or regeneration

### Token Limit Exceeded
- **Scenario**: Content or criteria exceed model limits
- **Behavior**: Truncate content or split processing
- **Monitoring**: Track token usage patterns
- **Optimization**: Implement content chunking strategies

## Performance Considerations

### Asynchronous Processing
- **LLM Operations**: Non-blocking execution using thread pools
- **Storage Operations**: Async GCS operations
- **Parallel Evaluation**: Potential for concurrent criterion processing

### Caching Strategy
- **Criteria Caching**: Avoid unnecessary regeneration
- **Token Preservation**: Maintain historical usage data
- **Result Caching**: Store evaluation results for retrieval

### Resource Optimization
- **Memory Management**: Efficient content processing
- **Network Efficiency**: Minimal GCS operations
- **Token Efficiency**: Precise counting and accumulation
