# Content Review Pipeline - Technical Implementation

## Overview

This document provides a technical implementation guide for the ProposalPro content review pipeline. The system evaluates user-provided content against two types of criteria: General Criteria (from a static rulebook) and Proposal-Specific Criteria (dynamically generated from the Main Document). The pipeline uses LLM-based evaluation to provide structured feedback, scores, and improvement suggestions.

## Architecture Flow

1. **Criteria Generation/Retrieval** → Load general criteria and generate/retrieve proposal-specific criteria
2. **Content Review Request** → User submits content with selected criteria for evaluation
3. **LLM-Based Evaluation** → Each criterion is applied individually to the content
4. **Structured Response Generation** → JSON-formatted results with scores and feedback
5. **Results Storage** → Save review results to GCS with cumulative token tracking

## Architecture Diagram

```mermaid
graph TB
    %% User Interface Layer
    User[👤 User] --> API{API Gateway}

    %% API Endpoints
    API --> GetCriteria[GET /content-review-criteria]
    API --> ReviewContent[POST /review-content]
    API --> GetResults[GET /content-review]

    %% Criteria Management Flow
    GetCriteria --> CriteriaManager{Criteria Manager}
    CriteriaManager --> GeneralCriteria[📋 General Criteria<br/>Static Rulebook]
    CriteriaManager --> ProposalCriteria{Proposal Criteria<br/>Decision Point}

    %% General Criteria Path
    GeneralCriteria --> RulebookGCS[(📁 GCS Rulebook<br/>content_review_rulebook.json)]

    %% Proposal Criteria Decision Flow
    ProposalCriteria --> CheckExisting{Check Existing<br/>Criteria}
    CheckExisting -->|Exists & Valid| ReturnCached[Return Cached Criteria]
    CheckExisting -->|Missing/Empty| CheckMainDoc{Main Document<br/>Available?}
    CheckExisting -->|Regenerate Flag| ForceGenerate[Force Regeneration]

    %% Main Document Processing
    CheckMainDoc -->|Yes| LoadMainDoc[📄 Load Main Document]
    CheckMainDoc -->|No| EmptyCriteria[Return Empty Criteria]
    ForceGenerate --> LoadMainDoc

    LoadMainDoc --> ProcessContent[🔄 Process Document Content]
    ProcessContent --> GeneratePrompt[📝 Generate RFP-Specific Prompt]
    GeneratePrompt --> LLMGeneration[🤖 LLM Criteria Generation]
    LLMGeneration --> ParseCriteria[📊 Parse & Validate JSON]
    ParseCriteria --> SaveCriteria[💾 Save to GCS]
    SaveCriteria --> ReturnGenerated[Return Generated Criteria]

    %% Content Review Flow
    ReviewContent --> ValidateRequest[✅ Validate Request]
    ValidateRequest --> LoadProject[📂 Load Project Context]
    LoadProject --> InitLLM[🤖 Initialize LLM]
    InitLLM --> ProcessCriteria{Process Each Criterion}

    %% Individual Criterion Processing
    ProcessCriteria --> GenerateReviewPrompt[📝 Generate Review Prompt]
    GenerateReviewPrompt --> CountInputTokens[🔢 Count Input Tokens]
    CountInputTokens --> LLMEvaluation[🤖 LLM Evaluation]
    LLMEvaluation --> CountOutputTokens[🔢 Count Output Tokens]
    CountOutputTokens --> ParseResponse[📊 Parse JSON Response]
    ParseResponse --> AccumulateResults[📈 Accumulate Results]

    %% Results Processing
    AccumulateResults --> MoreCriteria{More Criteria?}
    MoreCriteria -->|Yes| ProcessCriteria
    MoreCriteria -->|No| LoadPreviousTokens[📊 Load Previous Token Counts]
    LoadPreviousTokens --> CalculateTotal[🧮 Calculate Total Tokens]
    CalculateTotal --> SaveResults[💾 Save Review Results]
    SaveResults --> ReturnResults[📤 Return Results to User]

    %% Get Results Flow
    GetResults --> LoadStoredResults[📁 Load Stored Results]
    LoadStoredResults --> SanitizeContent[🔒 Sanitize Content]
    SanitizeContent --> ReturnStoredResults[📤 Return Historical Results]

    %% Storage Layer
    SaveCriteria --> ProposalCriteriaGCS[(📁 GCS Project Storage<br/>generated_proposal_specific_<br/>evaluation_parameters.json)]
    SaveResults --> ReviewResultsGCS[(📁 GCS Project Storage<br/>generated_content_review.json)]
    LoadStoredResults --> ReviewResultsGCS
    ReturnCached --> ProposalCriteriaGCS

    %% Database Layer
    LoadProject --> MongoDB[(🗄️ MongoDB<br/>Project Metadata)]
    LoadMainDoc --> ProjectGCS[(📁 GCS Project Storage<br/>Pickled Documents)]

    %% Error Handling
    ParseResponse -->|JSON Error| SkipCriterion[⚠️ Skip Criterion<br/>Continue Processing]
    SkipCriterion --> MoreCriteria
    LLMEvaluation -->|Error| LogError[📝 Log Error<br/>Continue Processing]
    LogError --> MoreCriteria

    %% Styling
    classDef userClass fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef apiClass fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef processClass fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef storageClass fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef llmClass fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef decisionClass fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    classDef errorClass fill:#ffebee,stroke:#b71c1c,stroke-width:2px

    class User userClass
    class API,GetCriteria,ReviewContent,GetResults apiClass
    class ProcessContent,ValidateRequest,LoadProject,GeneratePrompt,GenerateReviewPrompt,CountInputTokens,CountOutputTokens,ParseResponse,AccumulateResults,LoadPreviousTokens,CalculateTotal,SaveResults,LoadStoredResults,SanitizeContent processClass
    class RulebookGCS,ProposalCriteriaGCS,ReviewResultsGCS,MongoDB,ProjectGCS storageClass
    class LLMGeneration,InitLLM,LLMEvaluation llmClass
    class CriteriaManager,ProposalCriteria,CheckExisting,CheckMainDoc,ProcessCriteria,MoreCriteria decisionClass
    class SkipCriterion,LogError errorClass
```

## Stage 1: Content Review Criteria Generation and Retrieval

### Criteria Types and Sources

The content review system operates with two distinct types of evaluation criteria:

**General Criteria (Static)**:
- **Source**: Static rulebook stored in GCS at `CONTENT_REVIEW_RULEBOOK/content_review_rulebook.json`
- **Content**: Universal evaluation rules applicable to any proposal content
- **Structure**: JSON array with `name`, `type`, and `check` fields
- **Token Cost**: No tokens required (static retrieval)

**Proposal-Specific Criteria (Dynamic)**:
- **Source**: Generated from Main Document content using LLM
- **Content**: Customized evaluation rules specific to the RFP requirements
- **Generation Process**: Uses Main Document content + general criteria template
- **Token Cost**: Tracked and accumulated across generations

### Technical Implementation - Criteria Retrieval

**Function**: `get_content_review_criteria()`
**Process**: Intelligent criteria management with caching and regeneration logic

```python
# Decision flow for proposal criteria:
if regenerate_requested:
    generate_new_criteria()
    preserve_existing_token_counts()
    save_updated_criteria()
elif no_existing_criteria:
    generate_initial_criteria()
    save_new_criteria()
elif existing_criteria_empty and main_document_available:
    regenerate_criteria()
    save_updated_criteria()
else:
    return_cached_criteria()
```

### Proposal-Specific Criteria Generation Process

**Input Requirements**:
- Main Document content (tagged with `DocumentTags.MAIN_DOCUMENT`)
- General criteria template from rulebook
- Project's configured LLM model

**Content Processing Pipeline**:
1. **Document Retrieval**: Load pickled Main Document content from GCS
2. **Content Extraction**: Parse document sections using same logic as outline generation
3. **Content Validation**: Filter out extraction errors and empty sections
4. **Prompt Generation**: Combine content with general criteria using RFP-specific prompt
5. **LLM Processing**: Generate customized criteria with 8000 token limit
6. **Response Parsing**: Extract and validate JSON array structure
7. **Storage**: Save to project-specific path with token metadata

## Stage 2: Content Review Request Processing

### Request Structure and Validation

**Input Components**:
- `content_to_be_reviewed`: Text content for evaluation
- `criteria`: Array of evaluation rules (from criteria endpoint)

**Validation Process**:
- Project existence and user access verification
- Criteria format validation (name, type, check fields)
- Content length and format validation
- LLM model availability confirmation

### Technical Implementation - Request Flow

**Route**: `POST /projects/{project_id}/review-content`
**Process**: Sequential evaluation of each criterion against content

```python
# Processing flow:
for each_criterion in criteria:
    generate_evaluation_prompt(content, criterion)
    count_input_tokens(prompt)
    invoke_llm(prompt)
    count_output_tokens(response)
    parse_json_response(response)
    accumulate_results()
```

## Stage 3: LLM-Based Content Evaluation

### Evaluation Prompt Structure

**Prompt Components**:
- Professional reviewer role definition
- Single-criterion evaluation instruction
- Strict JSON output format specification
- Evaluation rule details
- Content to be reviewed

**Output Format Requirements**:
```json
{
  "rule_name": "Criterion name",
  "rule_type": "Criterion category",
  "score": "X/10 or N/A",
  "feedback": "Detailed evaluation observation",
  "suggestions": "Improvement recommendations or N/A"
}
```

### LLM Processing Implementation

**Model Support**:
- Google Generative AI models (experimental)
- Standard Vertex AI models
- Project-specific model configuration

**Token Management**:
- Input token counting per criterion
- Output token counting per response
- Cumulative tracking across all evaluations
- Preservation of historical token counts

**Error Handling**:
- JSON parsing failure recovery
- Individual criterion failure isolation
- Partial result preservation
- Detailed error logging

## Stage 4: Results Processing and Storage

### Response Structure Generation

**Individual Result Format**:
- `rule_name`: Applied criterion name
- `rule_type`: Criterion category
- `score`: Numerical or qualitative score
- `feedback`: Detailed evaluation feedback
- `suggestions`: Improvement recommendations

**Aggregated Response Format**:
- `content`: Array of individual results
- `input_tokens`: Cumulative input token count
- `output_tokens`: Cumulative output token count

### Storage Implementation

**Storage Location**: `{project_prefix}content_review/generated_content_review.json`

**Data Persistence**:
- Review results without original content
- Cumulative token counts (current + historical)
- Timestamp metadata through project manager
- JSON format with proper indentation

**Token Accumulation Logic**:
```python
# Token preservation across reviews:
previous_tokens = load_existing_review_data()
current_tokens = calculate_current_review_tokens()
accumulated_tokens = previous_tokens + current_tokens
save_accumulated_totals(accumulated_tokens)
```

## Stage 5: Results Retrieval

### GET Endpoint Implementation

**Route**: `GET /projects/{project_id}/content-review`
**Purpose**: Retrieve previously generated content review results

**Response Processing**:
- Load stored review data from GCS
- Remove sensitive content fields for security
- Maintain backward compatibility
- Update project visit timestamps

**Error Handling**:
- Missing review data detection
- Graceful degradation for incomplete data
- User-friendly error messages
- Proper HTTP status codes

## Key Technical Features

### Intelligent Criteria Management
- **Automatic Generation**: Creates proposal-specific criteria when Main Document is available
- **Caching Strategy**: Preserves generated criteria to avoid unnecessary regeneration
- **Smart Regeneration**: Detects when new Main Document requires criteria update
- **Token Preservation**: Maintains cumulative token counts across operations

### Robust Error Handling
- **Partial Failure Recovery**: Continues evaluation even if individual criteria fail
- **JSON Parsing Resilience**: Handles malformed LLM responses gracefully
- **Storage Failure Tolerance**: Continues operation even if save operations fail
- **Detailed Logging**: Comprehensive error tracking and debugging information

### Performance Optimization
- **Asynchronous Processing**: Non-blocking LLM operations using thread pools
- **Efficient Storage**: Minimal data persistence without content duplication
- **Token Efficiency**: Precise token counting and accumulation
- **Caching Strategy**: Intelligent criteria reuse to minimize LLM calls

### Security and Access Control
- **User Authentication**: Integrated with project-based access control
- **Content Sanitization**: Removes sensitive content from stored results
- **Project Isolation**: Strict project-based data separation
- **Audit Trail**: Comprehensive logging for security monitoring

## Configuration and Dependencies

### Required Components
- **Google Cloud Storage**: For criteria rulebook and results storage
- **MongoDB**: For project metadata and document tracking
- **LLM Integration**: Vertex AI or Google Generative AI models
- **Authentication System**: User access control and project permissions

### Storage Paths
- **Rulebook**: `CONTENT_REVIEW_RULEBOOK/content_review_rulebook.json`
- **Project Criteria**: `{project_prefix}content_review/generated_proposal_specific_evaluation_parameters.json`
- **Review Results**: `{project_prefix}content_review/generated_content_review.json`

### Token Management
- **Input Tracking**: Per-prompt token counting
- **Output Tracking**: Per-response token counting
- **Cumulative Storage**: Historical token preservation
- **Cost Monitoring**: Detailed usage analytics

## Implementation Examples

### Example 1: Criteria Generation Flow

```python
# Generate proposal-specific criteria
async def generate_proposal_specific_criteria(project_manager):
    # 1. Find Main Document
    project_doc = await projects_collection.find_one({"project_id": project_manager.project_id})
    main_document_filename = find_main_document(project_doc)

    # 2. Load and process content
    pickled_data = await project_manager.get_pickled_content(main_document_filename)
    sections = pickle.loads(pickled_data)
    combined_content = process_document_sections(sections)

    # 3. Generate criteria using LLM
    proposal_criterias = get_content_review_rulebook("proposal_criterias")
    prompt = get_rfp_specific_review_prompt(combined_content, proposal_criterias)

    # 4. LLM processing with token tracking
    llm = get_llm_instance(model_name=project_manager.model_name)
    input_tokens = count_tokens(prompt)
    response = await llm.invoke(prompt)
    output_tokens = count_tokens(response)

    # 5. Parse and return structured result
    parsed_criteria = clean_json_code_block(response)
    return {
        "content": parsed_criteria,
        "input_tokens": input_tokens,
        "output_tokens": output_tokens
    }
```

### Example 2: Content Review Evaluation

```python
# Review content against multiple criteria
async def review_content(project_id, content, criteria):
    # 1. Initialize tracking
    review_results = []
    total_input_tokens = 0
    total_output_tokens = 0

    # 2. Process each criterion individually
    for rule in criteria:
        # Generate evaluation prompt
        prompt = get_review_prompt(content, rule)
        input_tokens = count_tokens(prompt)

        # Get LLM evaluation
        response = await llm.invoke(prompt)
        output_tokens = count_tokens(response)

        # Parse structured response
        json_result = parse_json_proposal_info(response)
        review_results.append(json_result)

        # Accumulate tokens
        total_input_tokens += input_tokens
        total_output_tokens += output_tokens

    # 3. Combine with historical data
    previous_data = load_existing_review_data()
    accumulated_input = total_input_tokens + previous_data.get("input_tokens", 0)
    accumulated_output = total_output_tokens + previous_data.get("output_tokens", 0)

    # 4. Return structured results
    return {
        "content": review_results,
        "input_tokens": accumulated_input,
        "output_tokens": accumulated_output
    }
```

### Example 3: Criteria Retrieval Logic

```python
# Intelligent criteria management
async def get_content_review_criteria(project_manager, criterias_type=None, regenerate=False):
    result = {}

    # Handle general criteria (always from rulebook)
    if criterias_type is None or criterias_type == "general_criterias":
        general_criterias = get_content_review_rulebook("general_criterias")
        result["general_criterias"] = {
            "content": general_criterias,
            "input_tokens": 0,
            "output_tokens": 0
        }

    # Handle proposal criteria (generated or cached)
    if criterias_type is None or criterias_type == "proposal_criterias":
        if regenerate:
            # Force regeneration
            new_criteria = await generate_proposal_specific_criteria(project_manager)
            existing_data = await get_existing_proposal_specific_criteria(project_manager)

            # Preserve token history
            if existing_data:
                new_criteria["input_tokens"] += existing_data.get("input_tokens", 0)
                new_criteria["output_tokens"] += existing_data.get("output_tokens", 0)

            await save_proposal_specific_criteria(project_manager, new_criteria)
            result["proposal_criterias"] = new_criteria
        else:
            # Try to use existing, generate if needed
            existing_criteria = await get_existing_proposal_specific_criteria(project_manager)

            if not existing_criteria or not existing_criteria.get("content"):
                # Generate new criteria if none exist or empty
                if has_main_document(project_manager):
                    new_criteria = await generate_proposal_specific_criteria(project_manager)
                    await save_proposal_specific_criteria(project_manager, new_criteria)
                    result["proposal_criterias"] = new_criteria
                else:
                    # No Main Document available
                    result["proposal_criterias"] = {
                        "content": [],
                        "input_tokens": 0,
                        "output_tokens": 0
                    }
            else:
                # Use existing criteria
                result["proposal_criterias"] = existing_criteria

    return result
```

## API Endpoints Summary

### GET /projects/{project_id}/content-review-criteria
**Purpose**: Retrieve evaluation criteria for content review
**Parameters**:
- `criterias_type`: Optional filter ("general_criterias" or "proposal_criterias")
**Response**: Criteria data with token metadata
**Key Features**:
- Automatic proposal criteria generation
- Intelligent caching and regeneration
- Token cost tracking

### POST /projects/{project_id}/review-content
**Purpose**: Evaluate content against provided criteria
**Request Body**:
- `content_to_be_reviewed`: Text content for evaluation
- `criteria`: Array of evaluation rules
**Response**: Structured review results with scores and feedback
**Key Features**:
- Individual criterion evaluation
- Cumulative token tracking
- Partial failure recovery

### GET /projects/{project_id}/content-review
**Purpose**: Retrieve previously generated content review results
**Response**: Stored review results with cumulative token counts
**Key Features**:
- Historical data retrieval
- Content sanitization
- Backward compatibility

## Error Handling and Edge Cases

### Missing Main Document
- **Scenario**: No file tagged with `DocumentTags.MAIN_DOCUMENT`
- **Behavior**: Return empty proposal criteria with zero token cost
- **User Impact**: Only general criteria available for review

### LLM Response Parsing Failures
- **Scenario**: Malformed JSON in LLM response
- **Behavior**: Skip individual criterion, continue with others
- **Logging**: Detailed error information for debugging
- **User Impact**: Partial results returned

### Storage Operation Failures
- **Scenario**: GCS write operations fail
- **Behavior**: Continue execution, log errors
- **User Impact**: Results still returned to user
- **Recovery**: Manual retry or regeneration

### Token Limit Exceeded
- **Scenario**: Content or criteria exceed model limits
- **Behavior**: Truncate content or split processing
- **Monitoring**: Track token usage patterns
- **Optimization**: Implement content chunking strategies

## Performance Considerations

### Asynchronous Processing
- **LLM Operations**: Non-blocking execution using thread pools
- **Storage Operations**: Async GCS operations
- **Parallel Evaluation**: Potential for concurrent criterion processing

### Caching Strategy
- **Criteria Caching**: Avoid unnecessary regeneration
- **Token Preservation**: Maintain historical usage data
- **Result Caching**: Store evaluation results for retrieval

### Resource Optimization
- **Memory Management**: Efficient content processing
- **Network Efficiency**: Minimal GCS operations
- **Token Efficiency**: Precise counting and accumulation

## Data Flow and Transformations

### Stage-by-Stage Data Flow

#### Stage 1 → Stage 2: Criteria Request to Criteria Management
**Input Format**: Project ID with optional criteria type filter and regeneration flag
**Output Format**: Structured criteria data with token metadata
**Transformation**: Request validation → Project loading → Criteria type routing

```json
// Input
{
  "project_id": "proj_123",
  "criterias_type": "proposal_criterias",
  "regenerate": false
}

// Output
{
  "proposal_criterias": {
    "content": [...],
    "input_tokens": 1250,
    "output_tokens": 890
  }
}
```

#### Stage 2 → Stage 3: General Criteria Retrieval
**Input Format**: Criteria type filter for general criteria
**Output Format**: Static rulebook criteria array
**Transformation**: GCS retrieval → JSON parsing → Criteria filtering

```json
// Input Filter
"general_criterias"

// Output
{
  "general_criterias": {
    "content": [
      {
        "name": "Proposal Content Clarity",
        "type": "General",
        "check": "Evaluate clarity and conciseness..."
      }
    ],
    "input_tokens": 0,
    "output_tokens": 0
  }
}
```

#### Stage 3 → Stage 4: Proposal Criteria Generation Decision
**Input Format**: Project context and Main Document availability
**Output Format**: Decision path for criteria generation or retrieval
**Transformation**: Document tag analysis → Generation decision → Cache validation

```json
// Decision Logic
{
  "has_main_document": true,
  "existing_criteria": null,
  "regenerate_requested": false,
  "decision": "generate_new_criteria"
}
```

#### Stage 4 → Stage 5: Main Document Processing to LLM Context
**Input Format**: Pickled document content and general criteria template
**Output Format**: Combined RFP context with criteria template
**Transformation**: Pickle loading → Content extraction → Context combination

```json
// Input
{
  "pickled_content": "binary_data",
  "general_criteria": [...],
  "project_context": {...}
}

// Output
{
  "combined_content": "RFP content text...",
  "criteria_template": [...],
  "content_length": 15000
}
```

#### Stage 5 → Stage 6: LLM Context to Proposal-Specific Criteria
**Input Format**: RFP content with criteria template and generation prompt
**Output Format**: Generated proposal-specific criteria with token counts
**Transformation**: Prompt generation → LLM invocation → JSON parsing → Validation

```json
// Input
{
  "prompt": "You are an expert RFP specific parameter creator...",
  "model_config": {
    "max_output_tokens": 8000
  }
}

// Output
{
  "content": [
    {
      "name": "Technical Solution Alignment",
      "type": "Factor 1",
      "check": "Evaluate how well the proposed technical solution..."
    }
  ],
  "input_tokens": 1250,
  "output_tokens": 890
}
```

#### Stage 6 → Stage 7: Content Review Request to Evaluation Processing
**Input Format**: Content text with selected criteria array
**Output Format**: Validated request with project context and LLM instance
**Transformation**: Request validation → Content preparation → LLM initialization

```json
// Input
{
  "content_to_be_reviewed": "This is the proposal content...",
  "criteria": [
    {
      "name": "Technical Solution Alignment",
      "type": "Factor 1",
      "check": "Evaluate technical solution alignment..."
    }
  ]
}

// Output
{
  "validated_content": "This is the proposal content...",
  "validated_criteria": [...],
  "llm_instance": "configured_model",
  "project_context": {...}
}
```

#### Stage 7 → Stage 8: Individual Criterion Evaluation Processing
**Input Format**: Single criterion with content and evaluation prompt
**Output Format**: Structured evaluation result with token counts
**Transformation**: Prompt generation → Token counting → LLM evaluation → Response parsing

```json
// Input (per criterion)
{
  "criterion": {
    "name": "Technical Solution Alignment",
    "type": "Factor 1",
    "check": "Evaluate technical solution..."
  },
  "content": "Proposal content text...",
  "prompt": "You are a professional proposal reviewer..."
}

// Output (per criterion)
{
  "rule_name": "Technical Solution Alignment",
  "rule_type": "Factor 1",
  "score": "8/10",
  "feedback": "The technical solution demonstrates strong alignment...",
  "suggestions": "Consider adding more specific implementation details...",
  "input_tokens": 450,
  "output_tokens": 120
}
```

#### Stage 8 → Stage 9: Results Accumulation and Token Management
**Input Format**: Individual evaluation results with token counts
**Output Format**: Aggregated results with cumulative token tracking
**Transformation**: Result collection → Token accumulation → Historical data integration

```json
// Input (multiple results)
[
  {
    "rule_name": "Technical Solution Alignment",
    "score": "8/10",
    "input_tokens": 450,
    "output_tokens": 120
  },
  {
    "rule_name": "Cost Effectiveness",
    "score": "7/10",
    "input_tokens": 420,
    "output_tokens": 110
  }
]

// Output (aggregated)
{
  "content": [
    {
      "rule_name": "Technical Solution Alignment",
      "rule_type": "Factor 1",
      "score": "8/10",
      "feedback": "...",
      "suggestions": "..."
    },
    {
      "rule_name": "Cost Effectiveness",
      "rule_type": "Factor 2",
      "score": "7/10",
      "feedback": "...",
      "suggestions": "..."
    }
  ],
  "current_input_tokens": 870,
  "current_output_tokens": 230,
  "previous_input_tokens": 1250,
  "previous_output_tokens": 890,
  "total_input_tokens": 2120,
  "total_output_tokens": 1120
}
```

#### Stage 9 → Stage 10: Results Storage and Response Preparation
**Input Format**: Complete review results with metadata
**Output Format**: Stored data in GCS and sanitized API response
**Transformation**: Content sanitization → GCS storage → Response formatting

```json
// Storage Format (GCS)
{
  "content": [...],
  "input_tokens": 2120,
  "output_tokens": 1120
}

// API Response Format
{
  "content": [...],
  "input_tokens": 2120,
  "output_tokens": 1120
}
```

#### Stage 10: Historical Results Retrieval
**Input Format**: Project ID for results lookup
**Output Format**: Previously stored review results
**Transformation**: GCS retrieval → Content sanitization → Response formatting

```json
// Input
{
  "project_id": "proj_123"
}

// Output
{
  "content": [
    {
      "rule_name": "Technical Solution Alignment",
      "rule_type": "Factor 1",
      "score": "8/10",
      "feedback": "...",
      "suggestions": "..."
    }
  ],
  "input_tokens": 2120,
  "output_tokens": 1120
}
```

## Error Handling and Recovery

### Stage-Specific Error Handling

**Stage 1: Criteria Request Processing**
- Project not found validation with 404 HTTP status
- Invalid criteria type parameter with 400 Bad Request
- User permission checks with 403 Forbidden responses
- Missing authentication token management
- Invalid project ID format handling

**Stage 2: General Criteria Retrieval**
- Rulebook file not found in GCS with NotFound exception
- JSON parsing errors with JSONDecodeError handling
- Invalid criteria structure validation
- GCS connectivity issues with retry mechanisms
- Corrupted rulebook data handling

**Stage 3: Proposal Criteria Decision Logic**
- Missing project metadata handling
- Invalid document tag processing
- Cache corruption detection and recovery
- Decision logic failures with fallback to empty criteria
- MongoDB connection errors with graceful degradation

**Stage 4: Main Document Processing**
- No Main Document found → Return empty criteria with zero tokens
- Pickle file corruption or missing file handling
- Content extraction failures with error logging
- Invalid document structure processing
- Memory issues with large document handling

**Stage 5: LLM Criteria Generation**
- Model initialization failures with ValueError exceptions
- LLM invocation timeouts and retry logic
- Token limit exceeded handling with content truncation
- Model-specific response processing errors
- Network connectivity issues with LLM services
- JSON parsing failures with fallback to raw content

**Stage 6: Content Review Request Validation**
- Empty content validation with descriptive messaging
- Invalid criteria format detection
- Missing required fields handling
- Content length validation and truncation
- Malformed request structure processing

**Stage 7: Individual Criterion Evaluation**
- LLM evaluation failures for specific criteria with skip logic
- JSON response parsing errors with criterion exclusion
- Token counting failures with estimation fallbacks
- Prompt generation errors with default templates
- Model response timeout handling with retry mechanisms
- Invalid evaluation format detection and correction

**Stage 8: Results Accumulation**
- Partial evaluation results handling with warning messages
- Token calculation errors with manual counting fallbacks
- Memory issues with large result sets
- Result validation failures with error flagging
- Inconsistent data format handling

**Stage 9: Storage Operations**
- GCS upload failures with retry mechanisms
- Storage quota exceeded management
- File corruption during write operations
- Concurrent access conflicts resolution
- Metadata update failures with rollback support

**Stage 10: Results Retrieval**
- Missing results file handling with appropriate error messages
- Corrupted stored data detection and recovery
- Version compatibility issues with data migration
- Access permission errors with user notification
- Content sanitization failures with safe defaults

### Recovery Mechanisms

**Automatic Fallbacks**:
- Main Document missing → Empty proposal criteria with zero token cost
- Criteria generation failure → Use cached criteria or empty set
- Individual criterion failure → Skip criterion, continue with others
- LLM failure → HTTP exception with detailed error context
- Storage failure → Retry with exponential backoff
- JSON parsing failure → Use raw content with warning

**Retry Logic**:
- GCS operations with automatic retry (3 attempts with exponential backoff)
- LLM invocation retry on timeout or rate limiting (2 attempts)
- Database operations with connection retry (3 attempts)
- Criteria generation retry on parsing failures (1 attempt)
- File processing retry on temporary failures (2 attempts)

**Error Reporting**:
- Comprehensive logging at each stage with context and timestamps
- HTTP exception handling with appropriate status codes (400, 404, 500)
- User-friendly error messages with actionable guidance
- Error context preservation for debugging and support
- Token usage reporting even during failures

**Graceful Degradation**:
- Partial criteria evaluation when some criteria fail
- Continue processing with available criteria
- Provide warnings for missing or incomplete evaluations
- Maintain system stability during component failures
- Return partial results with clear indication of issues

**Data Consistency**:
- Token count preservation during failures
- Atomic storage operations where possible
- Rollback mechanisms for failed multi-step operations
- Data validation before storage commits
- Consistency checks during retrieval operations

### Monitoring and Diagnostics

**Performance Monitoring**:
- Token usage tracking for cost management and optimization
- Processing time measurement for each evaluation stage
- Success/failure rate monitoring per criterion type
- Content quality metrics tracking and analysis
- LLM response time and reliability monitoring

**Diagnostic Information**:
- Detailed logging of each processing stage with timing
- Criteria generation success/failure tracking
- Individual criterion evaluation status monitoring
- Storage operation success tracking with error details
- User activity patterns and usage analytics

**Health Checks**:
- GCS connectivity validation with periodic tests
- MongoDB connection health monitoring
- LLM service availability checks with fallback detection
- Criteria generation pipeline status verification
- Content review processing capacity monitoring

**Alert Systems**:
- High failure rate detection with automatic notifications
- Token usage threshold monitoring with cost alerts
- Storage quota monitoring with proactive warnings
- Performance degradation detection with escalation
- Service dependency failure alerts with impact assessment
