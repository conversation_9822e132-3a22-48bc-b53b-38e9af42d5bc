"""Document Loading and Processing Module for RAG System

This module provides functionality for loading and preprocessing documents from various formats
(PDF, Word) with support for special formatting and vision-based text extraction. It integrates
with Vertex AI's Gemini vision model for enhanced text extraction from images.

Key Features:
- Multi-format document loading (PDF, Word)
- Vision-based text extraction for complex formatting
- Page-level image extraction and processing
- Async operations support
- Extensive error handling and logging
- Safety configuration for AI models
"""

import os
import random
import fitz  # PyMuPDF
from langchain_community.document_loaders import PyMuPDFLoader
from langchain_community.document_loaders.word_document import UnstructuredWordDocumentLoader
from vertexai.preview import generative_models
from vertexai.preview.generative_models import GenerativeModel, Image, HarmCategory, HarmBlockThreshold, SafetySetting, GenerationConfig
from langchain_google_vertexai import ChatVertexAI
from dotenv import load_dotenv
import logging
import vertexai
import tempfile
from typing import List, Dict, Optional, TYPE_CHECKING, Union
import asyncio
from aiofiles import os as aiofiles_os
import aiofiles
import shutil
from config import settings
import pickle
from utils.constants import DocumentTags

if TYPE_CHECKING:
    from models import ProjectDocument, DocumentSection

# Safety config
# safety_config = [
#     SafetySetting(
#         category=HarmCategory.HATE_SPEECH,
#         threshold=HarmBlockThreshold.BLOCK_NONE,
#     ),
#     SafetySetting(
#         category=HarmCategory.DANGEROUS_CONTENT,
#         threshold=HarmBlockThreshold.BLOCK_NONE,
#     ),
#     SafetySetting(
#         category=HarmCategory.HARASSMENT,
#         threshold=HarmBlockThreshold.BLOCK_NONE,
#     ),
#     SafetySetting(
#         category=HarmCategory.SEXUALLY_EXPLICIT,
#         threshold=HarmBlockThreshold.BLOCK_NONE,
#     ),
# ]

load_dotenv()

# Initialize Vertex AI
# vertexai.init(project="your-project-id", location="your-location")

# Initialize the vision model
# vision_model = GenerativeModel("gemini-pro-vision", safety_settings=safety_config)
vision_model = GenerativeModel("gemini-2.0-flash-exp")

class DocumentProcessor:
    def __init__(self, file_name: str, sections: List[Union[Dict, "DocumentSection"]]):
        self.file_name = file_name
        self.sections = sections
        self.tags = []  # Initialize empty tags

    @property
    def content(self) -> str:
        """Combine all section contents into a single string."""
        if isinstance(self.sections[0], dict):
            return "\n\n".join(section.get("content", "") for section in self.sections)
        else:
            return "\n\n".join(section.content for section in self.sections)

    def has_attribute(self, attr: str) -> bool:
        """Check if this object has a specific attribute."""
        return hasattr(self, attr)

async def process_pickled_content(pickled_content: bytes, file_name: str, tags: List[str] = None) -> "ProjectDocument":
    """Process pickled content into a DocumentProcessor object that mimics ProjectDocument interface.

    Args:
        pickled_content: The pickled content to process
        file_name: Name of the file
        tags: Optional list of tags to assign to the document

    Returns:
        ProjectDocument or DocumentProcessor: Processed document object
    """
    try:
        sections = pickle.loads(pickled_content)
        if isinstance(sections, list):
            processor = DocumentProcessor(file_name, sections)
            processor.tags = tags or []  # Assign tags if provided
            return processor
        # If it's already a ProjectDocument, update its tags
        if tags:
            sections.tags = tags
        return sections
    except Exception as e:
        logging.error(f"Error processing pickled content for {file_name}: {str(e)}")
        raise

async def extract_page_as_image(pdf_path, page_number, output_folder, doc_handle=None):
    """Extract a single page from a PDF as an image.

    Args:
        pdf_path: Path to the PDF file
        page_number: Zero-based page number to extract
        output_folder: Directory to save the extracted image
        doc_handle: Optional already-opened document handle to reuse

    Returns:
        str: Path to the saved image file, or None if extraction fails

    Features:
        - High-resolution image extraction (2x scaling)
        - Async file operations
        - Temporary file management
        - Error handling and logging
        - Document handle reuse for optimization
    """
    logging.info(f"Extracting page {page_number} from {pdf_path}")
    await aiofiles_os.makedirs(output_folder, exist_ok=True)

    pdf_document = None
    should_close_doc = False

    try:
        # Use provided document handle or open new one
        if doc_handle is not None:
            logging.info(f"OPTIMIZATION: Reusing document handle for image extraction on page {page_number}")
            pdf_document = doc_handle
            should_close_doc = False
        else:
            logging.info(f"FALLBACK: Opening new PDF document for image extraction on page {page_number}")
            pdf_document = fitz.open(pdf_path)
            should_close_doc = True

        if page_number < len(pdf_document):
            # Get the page
            page = pdf_document[page_number]

            # Get page as image (CPU-bound operation)
            pix = await asyncio.to_thread(
                lambda: page.get_pixmap(matrix=fitz.Matrix(2, 2))
            )

            # Save image with random number to avoid conflicts
            random_number = random.randint(1000, 9999)
            output_image_path = os.path.join(output_folder, f"extractedimage_{random_number}.jpg")
            await asyncio.to_thread(pix.save, output_image_path)

            logging.info(f"Saved image to {output_image_path}")
            return output_image_path
        else:
            logging.error(f"Page number {page_number} out of range")
            return None

    except Exception as e:
        logging.error(f"Error extracting page {page_number} as image: {str(e)}")
        return None
    finally:
        # Only close document if we opened it ourselves
        if pdf_document and should_close_doc:
            try:
                pdf_document.close()
            except Exception as e:
                logging.warning(f"Error closing PDF document: {str(e)}")

async def extract_text_with_vision(image_path):
    """Extract text from an image using Vertex AI's vision model.

    Args:
        image_path: Path to the image file

    Returns:
        str: Extracted text from the image, or empty string if extraction fails

    Features:
        - Integration with Vertex AI vision model
        - Async file operations
        - Error handling with logging
        - Configurable prompt from settings
    """
    if not image_path:
        return ""

    logging.info(f"Extracting text from image at {image_path}")
    try:
        # Load image file asynchronously
        async with aiofiles.open(image_path, mode='rb') as f:
            image_content = await f.read()

        # Load the image using Vertex AI Image class (CPU-bound)
        image = await asyncio.to_thread(
            lambda: Image.from_bytes(image_content)
        )

        # Generate content using the vision model with the configured prompt
        response = await asyncio.to_thread(
            lambda: vision_model.generate_content([image, settings.VISION_PROMPT])
        )

        if not response:
            logging.warning("No response received from vision model")
            return ""

        # Extract text from response
        extracted_text = response.text

        return extracted_text

    except Exception as e:
        logging.error(f"Error in vision processing: {str(e)}")
        return ""

async def load_pdf_or_doc(file_content: bytes, filename: str, sf_pages: List[int] = None):
    """Load and process PDF or Word documents with special formatting support.

    Args:
        file_content: Binary content of the file
        filename: Name of the file
        sf_pages: List of page numbers requiring special formatting processing

    Returns:
        List[Document]: List of processed document pages

    Features:
        - Support for PDF and Word documents
        - Special formatting page handling
        - Vision-based text extraction for complex pages
        - Metadata preservation
        - Async file operations
        - Temporary file management
    """
    logging.info(f"Loading document: {filename}")
    docs = []

    # Create temporary directory
    temp_dir = tempfile.mkdtemp()
    try:
        # Save bytes content to temporary file asynchronously
        temp_file_path = os.path.join(temp_dir, filename)
        async with aiofiles.open(temp_file_path, 'wb') as f:
            await f.write(file_content)

        # Load documents (CPU-bound)
        if filename.lower().endswith('.pdf'):
            loader = PyMuPDFLoader(temp_file_path)
        elif filename.lower().endswith(('.docx', '.doc')):
            loader = UnstructuredWordDocumentLoader(temp_file_path)
        else:
            logging.warning(f"Unsupported file type for {filename}")
            return docs

        loaded_docs = await asyncio.to_thread(loader.load)

        # Process each page
        for doc in loaded_docs:
            page_number = doc.metadata.get("page", 0)

            # Check if this page needs vision processing
            if sf_pages and page_number in sf_pages:
                logging.info(f"Processing SF page {page_number} with vision")
                # Extract page as image
                image_path = await extract_page_as_image(temp_file_path, page_number, temp_dir)
                # Get vision-processed content
                extracted_text = await extract_text_with_vision(image_path)
                if extracted_text:
                    doc.page_content = extracted_text

            # Update metadata
            doc.metadata.update({
                "source": filename,
                "page": page_number,
                "total_pages": len(loaded_docs)
            })
            docs.append(doc)

    finally:
        # Clean up temporary directory
        await asyncio.to_thread(lambda: shutil.rmtree(temp_dir, ignore_errors=True))

    return docs

async def select_relevant_documents(self, section: "DocumentSection", documents: List["ProjectDocument"]) -> List["ProjectDocument"]:
    """Select documents relevant to a specific section based on tags and content.

    Args:
        section: The section to find relevant documents for
        documents: List of all available documents

    Returns:
        List[ProjectDocument]: List of documents relevant to the section
    """
    logging.info(f"\nSelecting relevant documents for section: {section.section_title}")
    logging.info(f"Total available documents: {len(documents)}")

    # Filter out any non-ProjectDocument objects
    valid_documents = [doc for doc in documents if hasattr(doc, 'file_name')]
    if len(valid_documents) != len(documents):
        logging.warning(f"Filtered out {len(documents) - len(valid_documents)} invalid documents that were not ProjectDocument instances")

    for doc in valid_documents:
        logging.info(f"Document: {doc.file_name}, Tags: {doc.tags}")

    relevant_docs = []

    # First pass: Get documents with no tags or Main Document tag
    for doc in valid_documents:
        if not doc.tags or DocumentTags.MAIN_DOCUMENT in doc.tags:
            relevant_docs.append(doc)
            if not doc.tags:
                logging.info(f"Selected - No tags: {doc.file_name}")
            else:
                logging.info(f"Selected - {DocumentTags.MAIN_DOCUMENT}: {doc.file_name}")

    # Second pass: Get documents with matching section title
    for doc in valid_documents:
        if doc not in relevant_docs:  # Skip if already added
            if section.section_title.lower() in [tag.lower() for tag in doc.tags]:
                relevant_docs.append(doc)
                logging.info(f"Selected - Matching tag: {doc.file_name} (Tags: {doc.tags})")

    if not relevant_docs:
        logging.warning(f"No relevant documents found for section {section.section_title}. Using all valid documents.")
        return valid_documents

    logging.info(f"Selected {len(relevant_docs)} relevant documents for section {section.section_title}")
    return relevant_docs


