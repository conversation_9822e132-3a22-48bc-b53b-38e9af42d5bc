"""Content management routes module for handling edited content from the UI.

This module provides a FastAPI route for saving edited content with version history.
The endpoint handles proper authentication, logging, and Google Cloud Storage operations.
It maintains a limited history of versions (max 3) for each content type.
"""

from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel, field_validator
from typing import List
import logging

from utils.auth import get_current_active_user, User
from project_manager import ProjectManager
from utils import save_content_version
from utils.constants import HTTPStatus, ContentTypes, Sections

router = APIRouter()
logger = logging.getLogger(__name__)

class ContentItem(BaseModel):
    """Schema for individual content item."""
    section_title: str
    edited_content: str

class EditedContentRequest(BaseModel):
    """Schema for edited content request."""
    content_type: str  # "outline", "summaries", or "response_content"
    content: List[ContentItem]  # List of content items with section_title and edited_content

    @field_validator('content')
    @classmethod
    def validate_content_structure(cls, v, values):
        """Validate that content is properly structured with required keys."""
        if not isinstance(v, list):
            raise ValueError("Content must be a list of ContentItem objects")

        if not v:
            raise ValueError("Content list cannot be empty")

        for i, item in enumerate(v):
            # For summaries, validate section titles against standard sections
            content_type = values.data.get('content_type') if hasattr(values, 'data') else None
            if content_type == ContentTypes.SUMMARIES:
                section_title = item.section_title
                # Check if the section title exists in standard sections (case-insensitive)
                valid_section = None
                for standard_section in Sections.ALL_SECTIONS:
                    if section_title.lower() == standard_section.lower():
                        valid_section = standard_section
                        break

                if not valid_section:
                    raise ValueError(
                        f"Invalid section title '{section_title}' in content item {i}. Must be one of: {', '.join(Sections.ALL_SECTIONS)}"
                    )

                # Update to use correct capitalization
                item.section_title = valid_section

        return v

@router.post("/projects/{project_id}/save-edited-content")
async def save_edited_content(
    project_id: str,
    content_data: EditedContentRequest,
    current_user: User = Depends(get_current_active_user)
):
    """Save edited content and create a versioned copy.

    This endpoint supports editing and versioning for three content types:
    - outline: Project outlines
    - summaries: Section summaries
    - response_content: Generated response content

    Args:
        project_id (str): The ID of the project
        content_data (EditedContentRequest): The edited content data containing a list of sections
        current_user (User): The authenticated user

    Returns:
        dict: Result of the save operation with version information

    Content Structure:
        - For outlines: Single item with section_title="outline" and edited_content
        - For summaries: Multiple items, each with section_title and edited_content
        - For response_content: Single item with section_title="response_content" and edited_content

    Versioning:
        - Creates versioned snapshots in history/{content_type}/ folder
        - Maintains maximum 3 versions per content type
        - Updates main content files as source of truth
        - Preserves token counts and metadata for response_content

    Storage Locations:
        - Main files: outline.json, summaries.json, response_content.json
        - History: history/outline/, history/summarizations/, history/response_content/

    Raises:
        HTTPException: 403 for unauthorized access
                      404 if project not found
                      400 for invalid content type or structure
                      500 for other errors
    """
    logger.info(f"User {current_user.full_name} saving edited content for project {project_id}")

    # Load the project
    project_manager = await ProjectManager.load_project(project_id)
    if not project_manager:
        raise HTTPException(status_code=HTTPStatus.NOT_FOUND, detail="Project not found")

    # Check permissions
    if project_manager.project_data.get('user_id') != str(current_user.id):
        raise HTTPException(status_code=HTTPStatus.FORBIDDEN, detail="You don't have permission to edit this project")

    # Validate content type
    if content_data.content_type not in [ContentTypes.OUTLINE, ContentTypes.SUMMARIES, ContentTypes.RESPONSE_CONTENT]:
        raise HTTPException(
            status_code=HTTPStatus.BAD_REQUEST,
            detail=f"Invalid content type. Must be '{ContentTypes.OUTLINE}', '{ContentTypes.SUMMARIES}', or '{ContentTypes.RESPONSE_CONTENT}'"
        )

    # Validate content structure based on content type
    if content_data.content_type == ContentTypes.OUTLINE:
        # For outlines, ensure there's exactly one item with section_title "outline"
        if len(content_data.content) != 1:
            raise HTTPException(
                status_code=HTTPStatus.BAD_REQUEST,
                detail="Outline content must contain exactly one item"
            )

        if content_data.content[0].section_title.lower() != 'outline':
            raise HTTPException(
                status_code=HTTPStatus.BAD_REQUEST,
                detail="Outline content item must have section_title 'outline'"
            )

    elif content_data.content_type == ContentTypes.SUMMARIES:
        # For summaries, validation is already handled by the field validator
        # Just ensure we have at least one section
        if not content_data.content:
            raise HTTPException(
                status_code=HTTPStatus.BAD_REQUEST,
                detail="Summaries content must contain at least one section"
            )

    elif content_data.content_type == ContentTypes.RESPONSE_CONTENT:
        # For response content, ensure there's exactly one item with section_title "response_content"
        if len(content_data.content) != 1:
            raise HTTPException(
                status_code=HTTPStatus.BAD_REQUEST,
                detail="Response content must contain exactly one item"
            )

        if content_data.content[0].section_title.lower() != 'response_content':
            raise HTTPException(
                status_code=HTTPStatus.BAD_REQUEST,
                detail="Response content item must have section_title 'response_content'"
            )

    # Save the edited content
    try:
        # Convert ContentItem objects to dictionaries for the save_content_version function
        content_dicts = [
            {
                "section_title": item.section_title,
                "content": item.edited_content
            }
            for item in content_data.content
        ]

        result = await save_content_version(
            project_manager=project_manager,
            content_type=content_data.content_type,
            content=content_dicts,
            user_id=str(current_user.id)
        )

        # Update project timestamps
        await project_manager.update_timestamps(update_content=True, update_visit=True)

        return result
    except Exception as e:
        logger.error(f"Error saving edited content: {str(e)}")
        raise HTTPException(status_code=HTTPStatus.INTERNAL_SERVER_ERROR, detail=str(e))
