"""Content Version Management Routes Module

This module provides FastAPI routes for managing content versions including:
- Listing available versions for a specific content type
- Retrieving a specific version by ID
All endpoints handle proper authentication, logging, and database operations.
"""

from fastapi import APIRouter, Depends, HTTPException
import logging
import asyncio
import json
from bson import ObjectId
from google.cloud.exceptions import NotFound

from utils.auth import get_current_active_user, User
from project_manager import ProjectManager
from utils.database import content_versions_collection, projects_collection
from schemas import ContentVersionMetadata, ContentVersionsList, ContentVersion, DeleteVersionResponse, DeletedVersionInfo
from utils.constants import HTTPStatus, ContentTypes, VersionManagement, Sections, StoragePaths
from utils.content_management import extract_metadata_from_edited_proposal_info

router = APIRouter()
logger = logging.getLogger(__name__)

async def update_main_response_content_from_promoted_version(project_manager, promoted_version):
    """Update the main response_content.json file from the newly promoted active version.

    Args:
        project_manager: ProjectManager instance
        promoted_version: The version document that was promoted to active
    """
    try:
        logger.info(f"Updating main response_content.json from promoted version {promoted_version['_id']}")

        # Load the content from the promoted version file
        version_blob = project_manager.bucket.blob(promoted_version["file_path"])

        try:
            version_content = await asyncio.to_thread(version_blob.download_as_text)
            version_data = json.loads(version_content)
        except Exception as load_error:
            logger.error(f"Failed to load promoted response content version: {str(load_error)}")
            return

        # Update the main response_content.json file
        response_content_path = f"{project_manager.project_prefix}{StoragePaths.RESPONSE_CONTENT_FILE}"
        response_content_blob = project_manager.bucket.blob(response_content_path)

        try:
            await asyncio.to_thread(
                response_content_blob.upload_from_string,
                json.dumps(version_data, indent=2),
                content_type="application/json"
            )
            logger.info(f"Successfully updated main response_content.json from promoted version {promoted_version['_id']}")
        except Exception as upload_error:
            logger.error(f"Failed to update main response_content.json: {str(upload_error)}")

    except Exception as e:
        logger.error(f"Error updating main response_content.json from promoted version: {str(e)}")
        # Don't raise the error - this shouldn't fail the deletion operation


async def update_main_outline_from_promoted_version(project_manager, promoted_version):
    """Update the main outline.json file from the newly promoted active version.

    Args:
        project_manager: ProjectManager instance
        promoted_version: The version document that was promoted to active
    """
    try:
        logger.info(f"Updating main outline.json from promoted version {promoted_version['_id']}")

        # Load the content from the promoted version file
        version_blob = project_manager.bucket.blob(promoted_version["file_path"])

        try:
            version_content = await asyncio.to_thread(version_blob.download_as_text)
            version_data = json.loads(version_content)
        except Exception as load_error:
            logger.error(f"Failed to load promoted outline version: {str(load_error)}")
            return

        # Update the main outline.json file
        outline_path = f"{project_manager.project_prefix}{StoragePaths.OUTLINE_FILE}"
        outline_blob = project_manager.bucket.blob(outline_path)

        try:
            await asyncio.to_thread(
                outline_blob.upload_from_string,
                json.dumps(version_data, indent=2),
                content_type="application/json"
            )
            logger.info(f"Successfully updated main outline.json from promoted version {promoted_version['_id']}")
        except Exception as upload_error:
            logger.error(f"Failed to update main outline.json: {str(upload_error)}")

    except Exception as e:
        logger.error(f"Error updating main outline.json from promoted version: {str(e)}")
        # Don't raise the error - this shouldn't fail the deletion operation


async def update_main_summaries_from_promoted_version(project_manager, promoted_version):
    """Update the main summaries.json file from the newly promoted active version.

    Args:
        project_manager: ProjectManager instance
        promoted_version: The version document that was promoted to active
    """
    try:
        logger.info(f"Updating main summaries.json from promoted version {promoted_version['_id']}")

        # Load the content from the promoted version file
        version_blob = project_manager.bucket.blob(promoted_version["file_path"])

        try:
            version_content = await asyncio.to_thread(version_blob.download_as_text)
            version_data = json.loads(version_content)
        except Exception as load_error:
            logger.error(f"Failed to load promoted summaries version: {str(load_error)}")
            return

        # Update the main summaries.json file
        summaries_path = f"{project_manager.project_prefix}{StoragePaths.SUMMARIES_FILE}"
        summaries_blob = project_manager.bucket.blob(summaries_path)

        try:
            await asyncio.to_thread(
                summaries_blob.upload_from_string,
                json.dumps(version_data, indent=2),
                content_type="application/json"
            )
            logger.info(f"Successfully updated main summaries.json from promoted version {promoted_version['_id']}")
        except Exception as upload_error:
            logger.error(f"Failed to update main summaries.json: {str(upload_error)}")

    except Exception as e:
        logger.error(f"Error updating main summaries.json from promoted version: {str(e)}")
        # Don't raise the error - this shouldn't fail the deletion operation


async def update_rfp_metadata_from_promoted_version(project_manager, promoted_version):
    """Update rfp_project_metadata in MongoDB from the newly promoted active version.

    Uses LLM to directly extract only the required metadata fields from the Proposal Info
    content, following the same efficient approach used when content is edited. Includes
    change detection to avoid unnecessary LLM calls when content hasn't changed.

    Args:
        project_manager: ProjectManager instance
        promoted_version: The version document that was promoted to active
    """
    try:
        logger.info(f"Updating rfp_project_metadata from promoted version {promoted_version['_id']}")

        # Load the content from the promoted version file
        version_blob = project_manager.bucket.blob(promoted_version["file_path"])

        try:
            version_content = await asyncio.to_thread(version_blob.download_as_text)
            version_data = json.loads(version_content)
        except Exception as load_error:
            logger.error(f"Failed to load promoted version content: {str(load_error)}")
            return

        # Check if the version contains Proposal Info
        proposal_info_content = None
        if Sections.PROPOSAL_INFO in version_data:
            proposal_info_data = version_data[Sections.PROPOSAL_INFO]
            if isinstance(proposal_info_data, dict) and "content" in proposal_info_data:
                proposal_info_content = proposal_info_data["content"]

        if not proposal_info_content:
            logger.info("No Proposal Info content found in promoted version, clearing rfp_project_metadata")
            await projects_collection.update_one(
                {"project_id": project_manager.project_id},
                {"$set": {"rfp_project_metadata": {}}}
            )
            return

        # Check if the Proposal Info content has changed compared to the current active content
        # to avoid unnecessary LLM calls (same logic as in save_content_version)
        current_proposal_info_content = ""
        try:
            # Load the current summaries.json to get the active Proposal Info content
            summaries_path = f"{project_manager.project_prefix}{StoragePaths.SUMMARIES_FILE}"
            summaries_blob = project_manager.bucket.blob(summaries_path)
            current_summaries_text = await asyncio.to_thread(summaries_blob.download_as_text)
            current_summaries = json.loads(current_summaries_text)
            current_proposal_info_content = current_summaries.get(Sections.PROPOSAL_INFO, {}).get("content", "")
        except Exception as e:
            logger.warning(f"Could not load current summaries for comparison: {str(e)}")
            current_proposal_info_content = ""

        # Compare the content to see if metadata extraction is needed
        if current_proposal_info_content.strip() == proposal_info_content.strip():
            logger.info("Proposal Info content unchanged between current active and promoted version, no metadata extraction needed")
            return

        logger.info("Proposal Info content differs between current active and promoted version, extracting metadata using LLM")

        # Extract metadata directly using LLM (same approach as when content is edited)
        try:
            success, filtered_metadata, token_usage = await extract_metadata_from_edited_proposal_info(
                proposal_info_content, project_manager
            )

            if success:
                # Update MongoDB with the filtered metadata
                update_result = await projects_collection.update_one(
                    {"project_id": project_manager.project_id},
                    {"$set": {"rfp_project_metadata": filtered_metadata}}
                )

                logger.info(f"Updated rfp_project_metadata from promoted version. Matched: {update_result.matched_count}, Modified: {update_result.modified_count}")
                logger.info(f"Extracted {len(filtered_metadata)} metadata fields from promoted version")
                logger.info(f"LLM token usage - Input: {token_usage.get('input_tokens', 0)}, Output: {token_usage.get('output_tokens', 0)}")
            else:
                logger.error("Failed to extract metadata from promoted version using LLM")
                # Clear metadata if extraction fails
                await projects_collection.update_one(
                    {"project_id": project_manager.project_id},
                    {"$set": {"rfp_project_metadata": {}}}
                )

        except Exception as extract_error:
            logger.error(f"Error extracting metadata from promoted version: {str(extract_error)}")
            # Clear metadata if extraction fails
            await projects_collection.update_one(
                {"project_id": project_manager.project_id},
                {"$set": {"rfp_project_metadata": {}}}
            )

    except Exception as e:
        logger.error(f"Error updating rfp_project_metadata from promoted version: {str(e)}")
        # Don't raise the error - this shouldn't fail the deletion operation

@router.get("/projects/{project_id}/content-versions")
async def list_content_versions(
    project_id: str,
    content_type: str,
    current_user: User = Depends(get_current_active_user)
):
    """List all available versions for a specific content type.

    Args:
        project_id (str): The ID of the project
        content_type (str): Type of content ("outline", "summaries", or "response_content")
        current_user (User): The authenticated user making the request

    Returns:
        ContentVersionsList: List of version metadata with active version ID

    Raises:
        HTTPException: 400 for invalid content type
                      403 for unauthorized access
                      404 if project not found
                      500 for other errors
    """
    logger.info(f"User {current_user.full_name} listing {content_type} versions for project {project_id}")

    # Validate content type
    if content_type not in [ContentTypes.OUTLINE, ContentTypes.SUMMARIES, ContentTypes.RESPONSE_CONTENT]:
        raise HTTPException(
            status_code=HTTPStatus.BAD_REQUEST,
            detail=f"Invalid content type. Must be '{ContentTypes.OUTLINE}', '{ContentTypes.SUMMARIES}', or '{ContentTypes.RESPONSE_CONTENT}'"
        )

    # Load project to verify access
    project_manager = await ProjectManager.load_project(project_id)
    if not project_manager:
        raise HTTPException(status_code=HTTPStatus.NOT_FOUND, detail="Project not found")

    # Verify user has access to this project
    if project_manager.project_data.get("user_id") != str(current_user.id):
        raise HTTPException(
            status_code=HTTPStatus.FORBIDDEN,
            detail="You don't have permission to access this project"
        )

    try:
        # Query for all versions of this content type for the project
        versions_cursor = content_versions_collection.find(
            {"project_id": project_id, "content_type": content_type}
        ).sort("created_at", -1)  # Sort by creation time, newest first

        versions_list = await versions_cursor.to_list(length=None)

        # Transform MongoDB documents to Pydantic models
        version_models = []
        active_version_id = None

        for version in versions_list:
            # Convert ObjectId to string for JSON serialization
            version_id = str(version["_id"])

            # Check if this is the active version
            if version.get(VersionManagement.ACTIVE_FLAG, False):
                active_version_id = version_id

            # Create Pydantic model (excluding file_path for security)
            version_model = ContentVersionMetadata(
                id=version_id,
                project_id=version["project_id"],
                content_type=version["content_type"],
                created_at=version["created_at"],
                created_by=version["created_by"],
                is_active=version.get(VersionManagement.ACTIVE_FLAG, False),
                description=version.get("description")
            )
            version_models.append(version_model)

        # Update project visit timestamp
        await project_manager.update_timestamps(update_content=False, update_visit=True)

        return ContentVersionsList(versions=version_models, active_version_id=active_version_id)

    except Exception as e:
        logger.error(f"Error listing content versions: {str(e)}")
        raise HTTPException(
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving content versions: {str(e)}"
        )

@router.get("/projects/{project_id}/content/{content_type}/version/{version_id}")
async def get_content_version(
    project_id: str,
    content_type: str,
    version_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """Retrieve a specific version of content by ID.

    Args:
        project_id (str): The ID of the project
        content_type (str): Type of content ("outline", "summaries", or "response_content")
        version_id (str): ID of the version to retrieve
        current_user (User): The authenticated user making the request

    Returns:
        ContentVersion: Version metadata and content

    Raises:
        HTTPException: 400 for invalid content type or version ID
                      403 for unauthorized access
                      404 if project, version, or content not found
                      500 for other errors
    """
    logger.info(f"User {current_user.full_name} retrieving {content_type} version {version_id} for project {project_id}")

    # Validate content type
    if content_type not in [ContentTypes.OUTLINE, ContentTypes.SUMMARIES, ContentTypes.RESPONSE_CONTENT]:
        raise HTTPException(
            status_code=HTTPStatus.BAD_REQUEST,
            detail=f"Invalid content type. Must be '{ContentTypes.OUTLINE}', '{ContentTypes.SUMMARIES}', or '{ContentTypes.RESPONSE_CONTENT}'"
        )

    # Load project to verify access
    project_manager = await ProjectManager.load_project(project_id)
    if not project_manager:
        raise HTTPException(status_code=HTTPStatus.NOT_FOUND, detail="Project not found")

    # Verify user has access to this project
    if project_manager.project_data.get("user_id") != str(current_user.id):
        raise HTTPException(
            status_code=HTTPStatus.FORBIDDEN,
            detail="You don't have permission to access this project"
        )

    try:
        # Convert string ID to ObjectId for MongoDB query
        try:
            object_id = ObjectId(version_id)
        except Exception:
            raise HTTPException(
                status_code=HTTPStatus.BAD_REQUEST,
                detail="Invalid version ID format"
            )

        # Query for the specific version
        version = await content_versions_collection.find_one(
            {"_id": object_id, "project_id": project_id, "content_type": content_type}
        )

        if not version:
            raise HTTPException(
                status_code=HTTPStatus.NOT_FOUND,
                detail=f"Version {version_id} not found for {content_type}"
            )

        # Get the content from GCS
        file_path = version["file_path"]
        content_blob = project_manager.bucket.blob(file_path)

        try:
            # Download content from GCS
            loop = asyncio.get_event_loop()
            content_text = await loop.run_in_executor(None, content_blob.download_as_text)
            content_data = json.loads(content_text)

            # Ensure content is in the expected format for the ContentVersion model
            if content_type == ContentTypes.OUTLINE:
                # For outline, ensure it's a dictionary with at least the 'content' key (standardized)
                if isinstance(content_data, str):
                    logger.warning(f"Outline content for version {version_id} is a string, converting to dictionary")
                    content_data = {"content": content_data}
                elif not isinstance(content_data, dict):
                    logger.warning(f"Outline content for version {version_id} is not a dict, converting")
                    content_data = {"content": str(content_data)}
                elif isinstance(content_data, dict) and "content" not in content_data and "outline" not in content_data:
                    logger.warning(f"Outline content for version {version_id} is missing 'content' key")
                    content_data["content"] = str(content_data)
                elif isinstance(content_data, dict) and "outline" in content_data and "content" not in content_data:
                    # Migrate old 'outline' key to new 'content' key for backward compatibility
                    logger.info(f"Migrating outline content for version {version_id} from 'outline' to 'content' key")
                    content_data["content"] = content_data.pop("outline")
        except NotFound:
            raise HTTPException(
                status_code=HTTPStatus.NOT_FOUND,
                detail=f"Content file not found for version {version_id}"
            )
        except json.JSONDecodeError:
            # If JSON parsing fails, try to handle it as a plain string (for backward compatibility)
            logger.warning(f"Invalid JSON in version {version_id}, attempting to handle as string")
            try:
                # Read the raw content as text
                content_text = await loop.run_in_executor(None, content_blob.download_as_text)
                if content_type == ContentTypes.OUTLINE:
                    content_data = {"content": content_text}  # Use standardized "content" key
                else:
                    # For summaries, wrap in a dictionary with a default section
                    content_data = {"Unknown Section": {"content": content_text}}
            except Exception as inner_e:
                logger.error(f"Failed to handle content as string: {str(inner_e)}")
                raise HTTPException(
                    status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
                    detail=f"Invalid content format in version {version_id}"
                )

        # Create version metadata model (excluding file_path for security)
        version_metadata = ContentVersionMetadata(
            id=str(version["_id"]),
            project_id=version["project_id"],
            content_type=version["content_type"],
            created_at=version["created_at"],
            created_by=version["created_by"],
            is_active=version.get(VersionManagement.ACTIVE_FLAG, False),
            description=version.get("description")
        )

        # Update project visit timestamp
        await project_manager.update_timestamps(update_content=False, update_visit=True)

        return ContentVersion(metadata=version_metadata, content=content_data)

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error retrieving content version: {str(e)}")
        raise HTTPException(
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving content version: {str(e)}"
        )

@router.delete("/projects/{project_id}/content/{content_type}/version/{version_id}", response_model=DeleteVersionResponse)
async def delete_content_version(
    project_id: str,
    content_type: str,
    version_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """Delete a specific version of content by ID.

    If the deleted version is the active version, the most recent remaining version
    will be automatically promoted to become the new active version. The main content
    files (outline.json, summaries.json, response_content.json) will be updated with
    content from the newly promoted version to maintain consistency. For summaries,
    the rfp_project_metadata in MongoDB will also be updated to reflect the Proposal Info
    content from the newly promoted version.

    Args:
        project_id (str): The ID of the project
        content_type (str): Type of content ("outline", "summaries", or "response_content")
        version_id (str): ID of the version to delete
        current_user (User): The authenticated user making the request

    Returns:
        DeleteVersionResponse: Success message, deletion details, and promoted version info (if any)

    Raises:
        HTTPException: 400 for invalid content type or version ID
                      403 for unauthorized access
                      404 if project or version not found
                      500 for other errors
    """
    logger.info(f"User {current_user.full_name} deleting {content_type} version {version_id} for project {project_id}")

    # Validate content type
    if content_type not in [ContentTypes.OUTLINE, ContentTypes.SUMMARIES, ContentTypes.RESPONSE_CONTENT]:
        raise HTTPException(
            status_code=HTTPStatus.BAD_REQUEST,
            detail=f"Invalid content type. Must be '{ContentTypes.OUTLINE}', '{ContentTypes.SUMMARIES}', or '{ContentTypes.RESPONSE_CONTENT}'"
        )

    # Load project to verify access
    project_manager = await ProjectManager.load_project(project_id)
    if not project_manager:
        raise HTTPException(status_code=HTTPStatus.NOT_FOUND, detail="Project not found")

    # Verify user has access to this project
    if project_manager.project_data.get("user_id") != str(current_user.id):
        raise HTTPException(
            status_code=HTTPStatus.FORBIDDEN,
            detail="You don't have permission to access this project"
        )

    try:
        # Convert string ID to ObjectId for MongoDB query
        try:
            object_id = ObjectId(version_id)
        except Exception:
            raise HTTPException(
                status_code=HTTPStatus.BAD_REQUEST,
                detail="Invalid version ID format"
            )

        # Query for the specific version
        version = await content_versions_collection.find_one(
            {"_id": object_id, "project_id": project_id, "content_type": content_type}
        )

        if not version:
            raise HTTPException(
                status_code=HTTPStatus.NOT_FOUND,
                detail=f"Version {version_id} not found for {content_type}"
            )

        # Check if this is the active version - if so, we'll need to promote another version
        is_active_version = version.get(VersionManagement.ACTIVE_FLAG, False)
        new_active_version = None

        if is_active_version:
            # Find the most recent version (by created_at) that is not the one being deleted
            other_versions = await content_versions_collection.find(
                {
                    "_id": {"$ne": object_id},  # Exclude the version being deleted
                    "project_id": project_id,
                    "content_type": content_type
                }
            ).sort("created_at", -1).to_list(length=1)  # Get the most recent one

            if other_versions:
                new_active_version = other_versions[0]
                logger.info(f"Will promote version {new_active_version['_id']} to active after deleting current active version")
            else:
                logger.warning(f"No other versions found to promote after deleting active version {version_id}")
                # We'll still allow the deletion - this means no versions will be left

        # Delete the file from GCS
        file_path = version["file_path"]
        try:
            version_blob = project_manager.bucket.blob(file_path)
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(None, version_blob.delete)
            logger.info(f"Deleted version file from GCS: {file_path}")
        except NotFound:
            logger.warning(f"Version file not found in GCS: {file_path}")
        except Exception as gcs_error:
            logger.error(f"Error deleting version file from GCS: {str(gcs_error)}")
            # Continue with MongoDB deletion even if GCS deletion fails

        # Delete the version from MongoDB
        delete_result = await content_versions_collection.delete_one({"_id": object_id})

        if delete_result.deleted_count == 0:
            raise HTTPException(
                status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
                detail="Failed to delete version from database"
            )

        # If we deleted the active version and found a replacement, promote it to active
        if is_active_version and new_active_version:
            promote_result = await content_versions_collection.update_one(
                {"_id": new_active_version["_id"]},
                {"$set": {VersionManagement.ACTIVE_FLAG: True}}
            )

            if promote_result.modified_count > 0:
                logger.info(f"Successfully promoted version {new_active_version['_id']} to active")

                # Update main files with content from the promoted version
                if content_type == ContentTypes.SUMMARIES:
                    # Update both the main summaries.json file and the rfp_project_metadata
                    await update_main_summaries_from_promoted_version(project_manager, new_active_version)
                    await update_rfp_metadata_from_promoted_version(project_manager, new_active_version)
                elif content_type == ContentTypes.RESPONSE_CONTENT:
                    await update_main_response_content_from_promoted_version(project_manager, new_active_version)
                elif content_type == ContentTypes.OUTLINE:
                    await update_main_outline_from_promoted_version(project_manager, new_active_version)
            else:
                logger.error(f"Failed to promote version {new_active_version['_id']} to active")
                # Don't fail the deletion because of this - the version was already deleted
        elif is_active_version:
            # We deleted the active version and no other versions exist
            # Handle cleanup for each content type
            if content_type == ContentTypes.SUMMARIES:
                # Clear the rfp_project_metadata since there's no active Proposal Info
                logger.info("Deleted the last active summaries version, clearing rfp_project_metadata")
                try:
                    await projects_collection.update_one(
                        {"project_id": project_manager.project_id},
                        {"$set": {"rfp_project_metadata": {}}}
                    )
                    logger.info("Successfully cleared rfp_project_metadata")
                except Exception as clear_error:
                    logger.error(f"Failed to clear rfp_project_metadata: {str(clear_error)}")
                    # Don't fail the deletion because of this

            # Note: For outline and response_content, we don't need special cleanup
            # The main files will remain as they are until new content is generated
            logger.info(f"Deleted the last active {content_type} version, no other versions remain")

        # Update project visit timestamp
        await project_manager.update_timestamps(update_content=False, update_visit=True)

        logger.info(f"Successfully deleted {content_type} version {version_id} for project {project_id}")

        # Create the response using the Pydantic models (excluding file_path for security)
        deleted_version_info = DeletedVersionInfo(
            id=version_id,
            content_type=content_type,
            created_at=version["created_at"],
            description=version.get("description")
        )

        # Create promoted version info if applicable
        promoted_version_info = None
        if is_active_version and new_active_version:
            promoted_version_info = DeletedVersionInfo(
                id=str(new_active_version["_id"]),
                content_type=new_active_version["content_type"],
                created_at=new_active_version["created_at"],
                description=new_active_version.get("description")
            )

        # Create success message
        if promoted_version_info:
            message = f"Version {version_id} deleted successfully. Version {promoted_version_info.id} is now active."
        else:
            message = f"Version {version_id} deleted successfully"

        return DeleteVersionResponse(
            success=True,
            message=message,
            deleted_version=deleted_version_info,
            promoted_version=promoted_version_info
        )

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error(f"Error deleting content version: {str(e)}")
        raise HTTPException(
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
            detail=f"Error deleting content version: {str(e)}"
        )
