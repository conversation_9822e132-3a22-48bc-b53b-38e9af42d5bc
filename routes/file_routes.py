"""File management routes module for project document handling.

This module provides FastAPI routes for file operations within projects including:
- File upload with metadata and special page detection
- File tag management (viewing, assigning, removing)
- File deletion and renaming
- Project file listing
All endpoints handle proper authentication, logging, and vector database synchronization.
"""

from fastapi import APIRouter, Depends, HTTPException, File, Form, UploadFile, BackgroundTasks, Query
from typing import List
import json
import logging
import tempfile
import aiofiles
import pickle
import os
import asyncio

from utils.auth import get_current_active_user, User
from project_manager import ProjectManager, ProjectDocument
from schemas import FileRename
from utils import identify_special_pages, get_combined_content_from_processed_doc, run_relevant_agent, clean_markdown_header, parse_json_proposal_info, filter_rfp_project_metadata, convert_proposal_info_to_markdown, clean_text_content
from config import settings
from schemas import TagAssignment, TagRemoval
from utils.database import projects_collection
from Agentic_Extraction import *
from RAG.vector_db import load_vector_db
from RAG_processing import generate_summary_single_query, generate_summary_multiple_queries
from utils.llm_config import get_llm_instance
from utils.constants import HTTPStatus, DocumentTags, StoragePaths, FileTypes, Sections

router = APIRouter()
logger = logging.getLogger(__name__)

async def generate_proposal_info_summary(project_id: str, project_manager: ProjectManager):
    """Generate the Proposal Info summary section in the background.

    Args:
        project_id (str): The ID of the project
        project_manager (ProjectManager): The project manager instance

    Note:
        This function is meant to be run as a background task
    """
    logger.info(f"Starting background generation of {Sections.PROPOSAL_INFO} summary for project {project_id}")
    logger.info(f"Project manager responses: {list(project_manager.responses.keys())}")

    try:
        # Check if vector DB exists - ensure path has trailing slash for consistency
        vector_db_path = f"{project_manager.project_prefix}{StoragePaths.VECTOR_DB_FOLDER}"
        logger.info(f"Vector DB path: {vector_db_path}")

        from google.cloud import storage
        client = storage.Client()
        bucket = client.bucket(settings.BUCKET_NAME)
        index_blob = bucket.blob(f"{vector_db_path}/index.faiss")

        vector_db_exists = index_blob.exists()
        logger.info(f"Vector DB exists: {vector_db_exists}")
        logger.info(f"create_vector_db setting: {settings.create_vector_db}")

        if vector_db_exists and settings.create_vector_db:
            logger.info("Using vector DB for Proposal Info summary generation")

            # Initialize vector DB and LLM - ensure we're using the same path format
            vector_db = await load_vector_db(vector_db_path)
            llm = get_llm_instance(model_name=project_manager.model_name)

            # Generate summary using RAG
            if settings.RAG_USE_MULTIPLE_QUERIES:
                summarized_section = await generate_summary_multiple_queries(
                    "Proposal Info",
                    vector_db,
                    llm,
                    project_id=project_id,
                    user_guidelines=""
                )
            else:
                summarized_section = await generate_summary_single_query(
                    "Proposal Info",
                    vector_db,
                    llm,
                    project_id=project_id,
                    user_guidelines=""
                )

            # Get current token counts
            input_tokens = project_manager.responses.get("Proposal Info", {}).get("input_tokens", 0)
            output_tokens = project_manager.responses.get("Proposal Info", {}).get("output_tokens", 0)

            # Prepare response data
            response_data = {
                "content": clean_text_content(summarized_section["final_response"], mode='markdown'),
                "input_tokens": summarized_section["input_tokens"] + input_tokens,
                "output_tokens": summarized_section["output_tokens"] + output_tokens,
                "used_vector_db": True
            }
        else:
            logger.info("Using Agentic approach for Proposal Info summary generation")
            # Get pickled sections content
            sections_content = await project_manager.get_pickled_sections_content()
            logger.info(f"Sections content is None: {sections_content is None}")

            if sections_content is None:
                logger.error("No extracted section content found for Proposal Info generation")
                return

            logger.info(f"Number of section content items: {len(sections_content)}")
            for i, section in enumerate(sections_content):
                logger.info(f"Section {i} keys: {list(section.keys())}")

            section_content_to_use = None
            for section_content in sections_content:
                if Sections.PROPOSAL_INFO in section_content:
                    section_content_to_use = section_content[Sections.PROPOSAL_INFO]
                    logger.info(f"Found {Sections.PROPOSAL_INFO} in section content")
                    break

            if section_content_to_use is None:
                logger.error(f"No content found for {Sections.PROPOSAL_INFO} section")
                return

            logger.info(f"Section content to use type: {type(section_content_to_use)}")
            logger.info(f"Section content to use length: {len(section_content_to_use) if isinstance(section_content_to_use, (list, tuple)) else 'not a list/tuple'}")

            # Get current token counts
            input_tokens = project_manager.responses.get(Sections.PROPOSAL_INFO, {}).get("input_tokens", 0)
            output_tokens = project_manager.responses.get(Sections.PROPOSAL_INFO, {}).get("output_tokens", 0)

            # Generate summary using Agentic approach
            drafted_content, token_usage = await run_relevant_agent(Sections.PROPOSAL_INFO, "Drafting", section_content_to_use[0], "")

            if not drafted_content:
                logger.error(f"Failed to draft {Sections.PROPOSAL_INFO} summary")
                return

            cleaned_drafted_content = clean_markdown_header(drafted_content)

            # Calculate token usage
            new_input_tokens = 0
            new_output_tokens = 0
            if token_usage:
                if hasattr(token_usage, 'prompt_tokens'):
                    new_input_tokens = token_usage.prompt_tokens
                    new_output_tokens = token_usage.completion_tokens

            # Prepare response data
            response_data = {
                "content": cleaned_drafted_content,
                "input_tokens": new_input_tokens + input_tokens,
                "output_tokens": new_output_tokens + output_tokens,
                "used_vector_db": False
            }

        # Parse the Proposal Info summary into structured metadata
        try:
            # Use cleaned_drafted_content directly for agentic approach, or response content for vector DB approach
            if response_data.get("used_vector_db", False):
                proposal_content = response_data.get("content", "")
            else:
                proposal_content = cleaned_drafted_content
            logger.info(f"Parsing {Sections.PROPOSAL_INFO} content of length {len(proposal_content)}")

            # Parse the full metadata
            full_metadata = parse_json_proposal_info(proposal_content)

            # Filter the metadata to only include the specified fields
            rfp_project_metadata = filter_rfp_project_metadata(full_metadata)

            logger.info(f"Parsed {len(full_metadata)} metadata fields, filtered to {len(rfp_project_metadata)} fields")
            logger.info(f"Filtered rfp_project_metadata: {rfp_project_metadata}")

            # Update MongoDB with filtered metadata only
            update_result = await projects_collection.update_one(
                {"project_id": project_id},
                {"$set": {
                    "rfp_project_metadata": rfp_project_metadata
                }}
            )

            # Convert the metadata to markdown table format and save to summaries.json
            try:
                # Use the full metadata for conversion to markdown
                markdown_content = convert_proposal_info_to_markdown(full_metadata)
                logger.info("Converted full rfp_project_metadata to markdown table format")

                # Update the response content with the markdown table
                response_data = {
                    "content": markdown_content,
                    "input_tokens": response_data.get("input_tokens", 0),
                    "output_tokens": response_data.get("output_tokens", 0),
                    "used_vector_db": response_data.get("used_vector_db", True)
                }
                logger.info(f"Prepared {Sections.PROPOSAL_INFO} response with markdown table content")
            except Exception as md_error:
                logger.error(f"Error converting metadata to markdown: {str(md_error)}")
                # Continue with original content if conversion fails

            logger.info(f"MongoDB update result: matched={update_result.matched_count}, modified={update_result.modified_count}")
        except Exception as parse_error:
            logger.error(f"Error parsing Proposal Info or updating MongoDB: {str(parse_error)}")
            # Update with empty dict in case of error
            await projects_collection.update_one(
                {"project_id": project_id},
                {"$set": {"rfp_project_metadata": {}}}
            )

        # Save response and update timestamps (only once, with final content)
        logger.info(f"Saving final response data: {response_data}")
        await project_manager.add_response(Sections.PROPOSAL_INFO, response_data)

        await project_manager.save_project()

        # Verify the response was saved correctly
        if Sections.PROPOSAL_INFO in project_manager.responses:
            saved_content = project_manager.responses[Sections.PROPOSAL_INFO].get("content", "")
            logger.info(f"Saved {Sections.PROPOSAL_INFO} content length: {len(saved_content)}")
            logger.info(f"Saved content is empty: {saved_content == ''}")
        else:
            logger.error(f"Failed to save {Sections.PROPOSAL_INFO} to responses")

        logger.info(f"Successfully generated {Sections.PROPOSAL_INFO} summary for project {project_id} in the background")

    except Exception as e:
        logger.error(f"Error generating {Sections.PROPOSAL_INFO} summary in background: {str(e)}")

@router.post("/projects/{project_id}/upload")
async def upload_files(
    project_id: str,
    background_tasks: BackgroundTasks,
    files: List[UploadFile] = File(...),
    metadata: str = Form(...),
    current_user: User = Depends(get_current_active_user)
):
    """Upload multiple files to a project with metadata and special page detection.

    Args:
        project_id (str): The ID of the project to upload files to
        background_tasks (BackgroundTasks): FastAPI background tasks handler
        files (List[UploadFile]): List of files to upload
        metadata (str): JSON string containing metadata for each file
        current_user (User): The authenticated user making the request

    Returns:
        dict: Success message with list of uploaded files

    Raises:
        HTTPException: 400 for invalid metadata or duplicate files
                      403 for unauthorized access
                      404 if project not found
                      500 for file processing errors
    """
    logger.info(f"User {current_user.full_name} uploading files to project {project_id}")

    try:
        # Log metadata for debugging
        logger.info(f"Received metadata: {repr(metadata[:200])}...")  # Log first 200 chars

        # Parse the metadata string into JSON
        metadata_list = json.loads(metadata)

        # Validate metadata structure
        if not isinstance(metadata_list, list):
            raise HTTPException(status_code=HTTPStatus.BAD_REQUEST, detail="Metadata must be a list")

        if not metadata_list:
            raise HTTPException(status_code=HTTPStatus.BAD_REQUEST, detail="Metadata list cannot be empty")

        # Validate each metadata item
        for i, item in enumerate(metadata_list):
            if not isinstance(item, dict):
                raise HTTPException(status_code=HTTPStatus.BAD_REQUEST, detail=f"Metadata item {i} must be an object")
            if "filename" not in item:
                raise HTTPException(status_code=HTTPStatus.BAD_REQUEST, detail=f"Metadata item {i} missing 'filename' field")

        # Create a mapping of filenames to their metadata
        metadata_map = {
            item["filename"]: {
                "tags": item.get("tags", []),
                "sf_pages": item.get("sf_pages", [])  # Add SF pages from metadata
            }
            for item in metadata_list
        }

        # Log uploaded file names vs metadata file names for debugging
        uploaded_filenames = [file.filename for file in files]
        metadata_filenames = list(metadata_map.keys())
        logger.info(f"Uploaded files: {uploaded_filenames}")
        logger.info(f"Metadata files: {metadata_filenames}")

    except json.JSONDecodeError as e:
        logger.error(f"JSON parsing failed: {str(e)}")
        logger.error(f"Raw metadata: {repr(metadata)}")
        raise HTTPException(status_code=HTTPStatus.BAD_REQUEST, detail=f"Invalid JSON in metadata: {str(e)}")
    except HTTPException:
        raise  # Re-raise HTTP exceptions
    except Exception as e:
        logger.error(f"Unexpected error processing metadata: {str(e)}")
        raise HTTPException(status_code=HTTPStatus.BAD_REQUEST, detail=f"Error processing metadata: {str(e)}")

    project_manager = await ProjectManager.load_project(project_id)
    if not project_manager:
        raise HTTPException(status_code=HTTPStatus.NOT_FOUND, detail="Project not found")

    if project_manager.project_data.get('user_id') != str(current_user.id):
        raise HTTPException(status_code=HTTPStatus.FORBIDDEN, detail="You don't have permission to access this project")

    # Get list of existing files
    existing_files = await project_manager.list_uploaded_files()

    # Check for duplicate files before processing
    for file in files:
        if file.filename in existing_files:
            raise HTTPException(
                status_code=HTTPStatus.BAD_REQUEST,
                detail=f"File '{file.filename}' already exists in the project"
            )

    processed_file_content_docs=[]

    # Create a temporary directory for file processing
    with tempfile.TemporaryDirectory() as temp_dir:
        uploaded_files = []
        for file in files:
            if file.filename not in metadata_map:
                continue

            file_metadata = metadata_map[file.filename]
            file_extension = file.filename.split('.')[-1].lower()

            # Save file to temporary directory
            temp_file_path = os.path.join(temp_dir, file.filename)
            try:
                # Write uploaded file to temp directory
                async with aiofiles.open(temp_file_path, 'wb') as f:
                    content = await file.read()
                    await f.write(content)

                # Read the file back from temp directory
                async with aiofiles.open(temp_file_path, 'rb') as f:
                    file_content = await f.read()

                # Identify special pages for PDF files marked as DocumentTags.MAIN_DOCUMENT
                sf_pages = file_metadata.get("sf_pages", [])
                identified_tables = None
                identified_images = None

                # Process PDF files for special pages
                if file_extension == 'pdf':
                    logger.info(f"Identifying special pages for PDF: {file.filename}")
                    identified_tables, identified_images = await identify_special_pages(file_content)
                    logger.info(f"Identified tables on pages: {identified_tables}")
                    logger.info(f"Identified images on pages: {identified_images}")
                    # Merge with any manually specified SF pages
                    sf_pages = list(set(sf_pages + identified_tables))
                    logger.info(f"Final SF pages for {file.filename}: {sf_pages}")
                    # Remove truncation
                    logger.info(f"Using all identified SF pages for {file.filename}: {sf_pages}")

                # Create ProjectDocument instance with special pages
                doc = await ProjectDocument.create(
                    file_content,
                    file.filename,
                    tags=file_metadata.get("tags"),
                    sf_pages=sf_pages,
                    identified_tables=identified_tables,
                    identified_images=identified_images
                )

                # Create pickle content from the already processed document
                pickled_content = pickle.dumps(doc.sections)

                # Upload original file and pickle to GCP
                await project_manager.save_uploaded_file(
                    file_content=file_content,
                    filename=file.filename,
                    tags=file_metadata.get("tags"),
                    sf_pages=sf_pages,
                    pickled_content=pickled_content,  # Pass the already created pickle
                    processed_doc=doc  # Pass the already processed document
                )

                uploaded_files.append(file.filename)
                processed_file_content_docs.append({"file_name":file.filename, "tags": file_metadata.get("tags"),"content":get_combined_content_from_processed_doc(doc)})

            except Exception as e:
                logger.error(f"Error processing file {file.filename}: {str(e)}")
                raise HTTPException(
                    status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
                    detail=f"Error processing file {file.filename}: {str(e)}"
                )

    # Only perform Agentic Extractions when create_vector_db is False
    if not settings.create_vector_db:
        logger.info("Starting Agentic Extractions since create_vector_db is False")
        logger.info(f"The length of processed_file_content_docs is {len(processed_file_content_docs)}")
        logger.info("Files being used for initial content extraction:")
        for doc in processed_file_content_docs:
            logger.info(f"  - {doc['file_name']} (Tags: {doc['tags']})")

        # Get all previously uploaded pickled content
        all_pickled_content = await project_manager.get_pickled_sections_content()
        section_wise_extracted_content = []

        # Initialize token counters
        total_input_tokens = 0
        total_output_tokens = 0

        # First, get existing section-wise content and their token counts
        existing_section_tokens = {}
        if all_pickled_content:
            logger.info("Found existing section-wise content")
            section_wise_extracted_content = all_pickled_content

            # Get existing token counts from responses
            for section_name in SECTIONS_TO_EXTRACT:
                if section_name in project_manager.responses:
                    response_data = project_manager.responses[section_name]
                    existing_section_tokens[section_name] = {
                        'input_tokens': response_data.get('input_tokens', 0),
                        'output_tokens': response_data.get('output_tokens', 0)
                    }
                    total_input_tokens += existing_section_tokens[section_name]['input_tokens']
                    total_output_tokens += existing_section_tokens[section_name]['output_tokens']
                    logger.info(f"Existing tokens for {section_name}: Input={existing_section_tokens[section_name]['input_tokens']}, Output={existing_section_tokens[section_name]['output_tokens']}")

        # Process only the newly uploaded files for section-wise extraction
        logger.info("Processing new files for section-wise extraction")
        extracted_content = await extract_content_for_each_section(processed_file_content_docs)

        # Merge new content with existing content or create new entries
        for section_dict in extracted_content:
            for section_name, (content, token_usage) in section_dict.items():
                # Find or create section in existing content
                existing_section = next((section for section in section_wise_extracted_content if section_name in section), None)

                if existing_section:
                    # Append new content to existing section
                    existing_content = existing_section[section_name][0] if isinstance(existing_section[section_name], tuple) else existing_section[section_name]
                    combined_content = f"{existing_content}\n\nAdditional Content:\n{content}"
                    existing_section[section_name] = (combined_content, token_usage)
                else:
                    # Create new section entry
                    section_wise_extracted_content.append({section_name: (content, token_usage)})

                # Update token counts
                # Handle different types of token_usage objects
                if token_usage:
                    if hasattr(token_usage, 'get'):
                        # If it's a dictionary-like object with get method
                        new_input_tokens = token_usage.get('input_tokens', 0)
                        new_output_tokens = token_usage.get('output_tokens', 0)
                    elif hasattr(token_usage, 'prompt_tokens'):
                        # If it's a UsageMetrics object from crewai
                        new_input_tokens = token_usage.prompt_tokens
                        new_output_tokens = token_usage.completion_tokens
                    elif hasattr(token_usage, 'input_tokens'):
                        # If it has direct input_tokens attribute
                        new_input_tokens = token_usage.input_tokens
                        new_output_tokens = getattr(token_usage, 'output_tokens', 0)
                    else:
                        # Default fallback
                        new_input_tokens = 0
                        new_output_tokens = 0
                else:
                    new_input_tokens = 0
                    new_output_tokens = 0

                # Add new tokens to existing tokens
                total_input_tokens += new_input_tokens
                total_output_tokens += new_output_tokens
                total_tokens = new_input_tokens + new_output_tokens

                # Store updated token counts in responses
                # Get existing token counts safely
                existing_input_tokens = 0
                existing_output_tokens = 0
                if section_name in existing_section_tokens:
                    existing_section = existing_section_tokens[section_name]
                    if isinstance(existing_section, dict) and 'input_tokens' in existing_section:
                        existing_input_tokens = existing_section.get('input_tokens', 0)
                        existing_output_tokens = existing_section.get('output_tokens', 0)

                # Get existing content if available
                existing_content = ""
                if section_name in project_manager.responses:
                    existing_content = project_manager.responses[section_name].get("content", "")

                # Only update token counts, preserve existing content
                response_data = {
                    "content": existing_content,  # Preserve existing content
                    "input_tokens": existing_input_tokens + new_input_tokens,
                    "output_tokens": existing_output_tokens + new_output_tokens,
                    "used_vector_db": project_manager.responses.get(section_name, {}).get("used_vector_db", False)
                }
                await project_manager.add_response(section_name, response_data)

                logger.info(f"Section: {section_name}")
                logger.info(f"  New input tokens: {new_input_tokens}")
                logger.info(f"  New output tokens: {new_output_tokens}")
                logger.info(f"  Total tokens for this section: {total_tokens}")
                logger.info("---")

        logger.info("Final token usage:")
        logger.info(f"  Total input tokens: {total_input_tokens}")
        logger.info(f"  Total output tokens: {total_output_tokens}")
        logger.info(f"  Total combined tokens: {total_input_tokens + total_output_tokens}")

        # Pickle and save the extracted content to GCP
        try:
            pickled_content = pickle.dumps(section_wise_extracted_content)

            # Create blob path under extracted_section_content folder
            extracted_content_blob = project_manager.bucket.blob(f"{project_manager.project_prefix}{StoragePaths.PROCESSED_DOCUMENTS_FOLDER}sections.pkl")

            # Upload pickled content to GCP
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                None,
                lambda: extracted_content_blob.upload_from_string(
                    pickled_content,
                    content_type="application/octet-stream"
                )
            )

            logger.info("Successfully saved pickled section content to GCP")
        except Exception as e:
            logger.error(f"Error saving pickled section content to GCP: {str(e)}")
            raise HTTPException(
                status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
                detail=f"Error saving extracted content: {str(e)}"
            )
    else:
        logger.info("Skipping Agentic Extractions since create_vector_db is True")

    await project_manager.save_project()
    # Update timestamps for the project when files are uploaded
    await project_manager.update_timestamps(update_content=True, update_visit=True)
    logger.info(f"Successfully uploaded files: {', '.join(uploaded_files)}")

    # Check if any file has DocumentTags.MAIN_DOCUMENT tag and if Sections.PROPOSAL_INFO summary needs to be generated
    logger.info("Checking if Proposal Info summary needs to be generated")
    has_main_document = False

    # First check in currently uploaded files
    logger.info(f"Checking for {DocumentTags.MAIN_DOCUMENT} tag in {len(processed_file_content_docs)} currently uploaded files")
    for file in processed_file_content_docs:
        logger.info(f"Checking file: {file.get('file_name', '[]')}")
        if DocumentTags.MAIN_DOCUMENT in file.get("tags", []):
            logger.info(f"Found {DocumentTags.MAIN_DOCUMENT} tag in currently uploaded file: {file.get('file_name')}")
            has_main_document = True
            break

    # If not found, check in all previously uploaded files in MongoDB
    if not has_main_document:
        logger.info(f"{DocumentTags.MAIN_DOCUMENT} tag not found in currently uploaded files, checking MongoDB")
        project = await projects_collection.find_one({"project_id": project_id})
        if project and "files_metadata" in project:
            logger.info(f"Checking {len(project.get('files_metadata', []))} files in MongoDB")
            for file_metadata in project.get("files_metadata", []):
                if DocumentTags.MAIN_DOCUMENT in file_metadata.get("tags", []):
                    has_main_document = True
                    logger.info(f"Found {DocumentTags.MAIN_DOCUMENT} tag in previously uploaded file: {file_metadata.get('filename')}")
                    break

    logger.info(f"Has {DocumentTags.MAIN_DOCUMENT} tag: {has_main_document}")

    if has_main_document:
        # Check if Proposal Info summary exists and is not empty
        proposal_info_exists = False

        # Debug the responses and content
        logger.info(f"Responses keys: {list(project_manager.responses.keys())}")
        if Sections.PROPOSAL_INFO in project_manager.responses:
            content = project_manager.responses[Sections.PROPOSAL_INFO].get("content", "")
            logger.info(f"{Sections.PROPOSAL_INFO} content: '{content}'")
            logger.info(f"Content type: {type(content)}")
            logger.info(f"Content is empty: {content == ''}")
            logger.info(f"Content stripped is empty: {not content.strip()}")

            # Only consider it exists if it has non-empty content
            if content and content.strip():
                proposal_info_exists = True
                logger.info(f"{Sections.PROPOSAL_INFO} exists with non-empty content")
            else:
                logger.info(f"{Sections.PROPOSAL_INFO} exists but content is empty or whitespace")
        else:
            logger.info(f"{Sections.PROPOSAL_INFO} section does not exist in responses")

        if not proposal_info_exists:
            logger.info(f"Starting background task to generate {Sections.PROPOSAL_INFO} summary")
            # Add background task to generate Proposal Info summary
            background_tasks.add_task(generate_proposal_info_summary, project_id, project_manager)

    return {"message": "Files uploaded successfully", "uploaded_files": uploaded_files}

@router.get("/projects/{project_id}/file-tags/{file_id}")
async def get_file_tags(
    project_id: str, file_id: str, current_user: User = Depends(get_current_active_user)
):
    """Retrieve tags associated with a specific file in a project.

    Args:
        project_id (str): The ID of the project containing the file
        file_id (str): The ID of the file to get tags for
        current_user (User): The authenticated user making the request

    Returns:
        dict: File ID, filename, and associated tags

    Raises:
        HTTPException: 403 for unauthorized access
                      404 if project or file not found
    """
    logger.info(
        f"User {current_user.full_name} requesting tags for file with id {file_id} in project {project_id}"
    )

    project_manager = await ProjectManager.load_project(project_id)
    if not project_manager:
        raise HTTPException(status_code=HTTPStatus.NOT_FOUND, detail="Project not found")

    if project_manager.project_data.get("user_id") != str(current_user.id):
        raise HTTPException(status_code=HTTPStatus.FORBIDDEN, detail="You don't have permission to access this project")

    project = await projects_collection.find_one({"project_id": project_id})
    file_metadata = next(
        (f for f in project.get("files_metadata", []) if f["file_id"] == file_id), None
    )
    if not file_metadata:
        raise HTTPException(status_code=HTTPStatus.NOT_FOUND, detail="File not found in the project")

    tags = await project_manager.get_document_tags(file_id)
    logger.info(f"Retrieved tags for file {file_id} in project {project_id}: {tags}")
    await project_manager.update_timestamps(update_content=False, update_visit=True)
    return {"file_id": file_id, "filename": file_metadata["filename"], "tags": tags}

@router.post("/projects/{project_id}/files/{file_id}/assign_tags")
async def assign_tags(
    project_id: str,
    file_id: str,
    tag_assignment: TagAssignment,
    current_user: User = Depends(get_current_active_user),
):
    """Assign new tags to a file in a project.

    Args:
        project_id (str): The ID of the project containing the file
        file_id (str): The ID of the file to assign tags to
        tag_assignment (TagAssignment): The tags to assign
        current_user (User): The authenticated user making the request

    Returns:
        dict: Success message with assigned tags

    Raises:
        HTTPException: 403 for unauthorized access
                      404 if project or file not found
    """
    logger.info(
        f"User {current_user.full_name} assigning tags to file {file_id} in project {project_id}"
    )
    project_manager = await ProjectManager.load_project(project_id)
    if not project_manager:
        raise HTTPException(status_code=HTTPStatus.NOT_FOUND, detail="Project not found")

    if project_manager.project_data.get("user_id") != str(current_user.id):
        raise HTTPException(status_code=HTTPStatus.FORBIDDEN, detail="You don't have permission to access this project")

    project = await projects_collection.find_one({"project_id": project_id})
    file_metadata = next(
        (f for f in project.get("files_metadata", []) if f["file_id"] == file_id), None
    )
    if not file_metadata:
        raise HTTPException(status_code=HTTPStatus.NOT_FOUND, detail="File not found in the project")

    # Determine which tags are new vs already existing
    current_tags = await project_manager.get_document_tags(file_id)
    new_tags = [tag for tag in tag_assignment.tags if tag not in current_tags]
    existing_tags = [tag for tag in tag_assignment.tags if tag in current_tags]

    if new_tags:
        # Update tags in project manager
        await project_manager.update_document_tags(file_id, current_tags + new_tags)

        if settings.create_vector_db:

            # Update tags in vector DB if it exists
            if os.path.exists(project_manager.vector_db_path):
                logger.info("Updating tags in vector DB")
                from RAG.vector_db import load_vector_db, update_document_tags_in_vectordb, save_vector_db

                try:
                    vector_db = await load_vector_db(project_manager.vector_db_path)

                    # Get the full file path from metadata
                    file_path = os.path.join(project_manager.project_prefix, file_metadata.get("filename", ""))
                    logger.info(f"Updating tags for file path: {file_path}")

                    if await update_document_tags_in_vectordb(vector_db, file_path, current_tags + new_tags):
                        await save_vector_db(vector_db, project_manager.vector_db_path)
                        logger.info("Successfully updated tags in vector DB")
                    else:
                        logger.error("Failed to update tags in vector DB")
                except Exception as e:
                    logger.error(f"Error updating vector DB: {str(e)}")

        message = f"Tags assigned successfully to file {file_metadata['filename']}: {', '.join(new_tags)}"
    elif existing_tags:
        message = f"All specified tags were already assigned to file {file_metadata['filename']}: {', '.join(existing_tags)}"
    else:
        message = f"No new tags were assigned to file {file_metadata['filename']}"

    logger.info(message)
    await project_manager.update_timestamps(update_content=True, update_visit=True)
    return {"message": message}

@router.post("/projects/{project_id}/files/{file_id}/remove_tags")
async def remove_tags(
    project_id: str,
    file_id: str,
    tag_removal: TagRemoval,
    current_user: User = Depends(get_current_active_user),
):
    """Remove tags from a file in a project.

    Args:
        project_id (str): The ID of the project containing the file
        file_id (str): The ID of the file to remove tags from
        tag_removal (TagRemoval): The tags to remove
        current_user (User): The authenticated user making the request

    Returns:
        dict: Success message with removed tags

    Raises:
        HTTPException: 403 for unauthorized access
                      404 if project or file not found
    """
    logger.info(
        f"User {current_user.full_name} removing tags from file {file_id} in project {project_id}"
    )
    project_manager = await ProjectManager.load_project(project_id)
    if not project_manager:
        raise HTTPException(status_code=HTTPStatus.NOT_FOUND, detail="Project not found")

    if project_manager.project_data.get("user_id") != str(current_user.id):
        raise HTTPException(
            status_code=HTTPStatus.FORBIDDEN, detail="You don't have permission to modify this project"
        )

    project = await projects_collection.find_one({"project_id": project_id})
    file_metadata = next(
        (f for f in project.get("files_metadata", []) if f["file_id"] == file_id), None
    )
    if not file_metadata:
        raise HTTPException(status_code=HTTPStatus.NOT_FOUND, detail="File not found in the project")

    # Determine which tags to remove and create new tag list
    current_tags = await project_manager.get_document_tags(file_id)
    tags_to_remove = [tag for tag in tag_removal.tags if tag in current_tags]
    new_tags = [tag for tag in current_tags if tag not in tag_removal.tags]

    if tags_to_remove:
        # Update tags in project manager
        await project_manager.update_document_tags(file_id, new_tags)

        if settings.create_vector_db:

            # Update tags in vector DB if it exists
            if os.path.exists(project_manager.vector_db_path):
                logger.info("Updating tags in vector DB after removal")
                from RAG.vector_db import load_vector_db, update_document_tags_in_vectordb, save_vector_db

                try:
                    vector_db = await load_vector_db(project_manager.vector_db_path)

                    # Get the full file path from metadata
                    file_path = os.path.join(project_manager.project_prefix, file_metadata.get("filename", ""))
                    logger.info(f"Updating tags for file path: {file_path}")

                    if await update_document_tags_in_vectordb(vector_db, file_path, new_tags):
                        await save_vector_db(vector_db, project_manager.vector_db_path)
                        logger.info("Successfully updated tags in vector DB")
                    else:
                        logger.error("Failed to update tags in vector DB")
                except Exception as e:
                    logger.error(f"Error updating vector DB: {str(e)}")

        message = f"Tags removed successfully from file {file_metadata['filename']}: {', '.join(tags_to_remove)}"
    else:
        message = f"No tags were removed from file {file_metadata['filename']}. The specified tags didn't exist for this file."

    logger.info(message)
    await project_manager.update_timestamps(update_content=True, update_visit=True)
    return {"message": message}

@router.delete("/projects/{project_id}/files/{file_id}")
async def delete_file(
    project_id: str,
    file_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """Delete a file from a project.

    Args:
        project_id (str): The ID of the project containing the file
        file_id (str): The ID of the file to delete
        current_user (User): The authenticated user making the request

    Returns:
        dict: Success message with deleted file ID

    Raises:
        HTTPException: 403 for unauthorized access
                      404 if project or file not found
    """
    logger.info(f"User {current_user.full_name} attempting to delete file with ID {file_id} from project {project_id}")
    project_manager = await ProjectManager.load_project(project_id)
    if not project_manager:
        raise HTTPException(status_code=HTTPStatus.NOT_FOUND, detail="Project not found")

    if project_manager.project_data.get('user_id') != str(current_user.id):
        raise HTTPException(status_code=HTTPStatus.FORBIDDEN, detail="You don't have permission to access this project")

    # Get file metadata before deletion
    project = await projects_collection.find_one({"project_id": project_id})
    file_metadata = next(
        (f for f in project.get("files_metadata", []) if f["file_id"] == file_id), None
    )

    if not file_metadata:
        logger.error(f"File with ID {file_id} not found in project {project_id}")
        raise HTTPException(status_code=HTTPStatus.NOT_FOUND, detail=f"File not found with ID: {file_id}")

    # Delete file from project manager
    success, message, filename = await project_manager.delete_uploaded_file_by_id(file_id, str(current_user.id))

    if success:
        if settings.create_vector_db:
            # Remove file chunks from vector DB if it exists
            if os.path.exists(project_manager.vector_db_path):
                logger.info(f"Removing file {filename} from vector DB")
                from RAG.vector_db import load_vector_db, remove_document_from_vectordb, save_vector_db

                try:
                    vector_db = await load_vector_db(project_manager.vector_db_path)

                    # Construct the full file path
                    file_path = os.path.join(project_manager.project_prefix, filename)
                    logger.info(f"Removing document with path: {file_path}")

                    if await remove_document_from_vectordb(vector_db, file_path):
                        await save_vector_db(vector_db, project_manager.vector_db_path)
                        logger.info("Successfully removed file from vector DB")
                    else:
                        logger.error("Failed to remove file from vector DB")
                except Exception as e:
                    logger.error(f"Error updating vector DB: {str(e)}")

        # Update timestamps for the project when files are deleted
        await project_manager.update_timestamps(update_content=True, update_visit=True)

        logger.info(f"Successfully deleted file {filename} (ID: {file_id}) from project {project_id}")
        return {"message": f"Successfully deleted file: {filename}", "file_id": file_id}
    elif message == "Unauthorized":
        logger.warning(f"Unauthorized attempt to delete file with ID {file_id} from project {project_id}")
        raise HTTPException(status_code=HTTPStatus.FORBIDDEN, detail="You don't have permission to delete this file")
    else:
        logger.error(f"File with ID {file_id} not found in project {project_id}")
        raise HTTPException(status_code=HTTPStatus.NOT_FOUND, detail=f"File not found with ID: {file_id}")

@router.put("/projects/{project_id}/rename-file")
async def rename_file(
    project_id: str,
    file_id: str,
    rename_data: FileRename,
    current_user: User = Depends(get_current_active_user)
):
    """Rename a file in a project.

    Args:
        project_id (str): The ID of the project containing the file
        file_id (str): The ID of the file to rename
        rename_data (FileRename): The new name for the file
        current_user (User): The authenticated user making the request

    Returns:
        dict: Success message

    Raises:
        HTTPException: 403 for unauthorized access
                      404 if project or file not found
                      409 if new filename already exists
                      500 for other errors
    """
    logger.info(f"User {current_user.full_name} attempting to rename file {file_id} in project {project_id}")

    project_manager = await ProjectManager.load_project(project_id)
    if not project_manager:
        raise HTTPException(status_code=HTTPStatus.NOT_FOUND, detail="Project not found")

    if project_manager.project_data.get('user_id') != str(current_user.id):
        raise HTTPException(status_code=HTTPStatus.FORBIDDEN, detail="You don't have permission to modify this project")

    success, message = await project_manager.rename_file(file_id, rename_data.new_name)

    await project_manager.update_timestamps(update_content=True, update_visit=True)

    if success:
        logger.info(message)
        return {"message": message}
    else:
        logger.error(f"Failed to rename file in project {project_id}: {message}")
        if "already exists" in message:
            raise HTTPException(status_code=409, detail=message)
        elif "not found" in message:
            raise HTTPException(status_code=404, detail=message)
        else:
            raise HTTPException(status_code=500, detail=message)

@router.get("/projects/{project_id}/files")
async def get_project_files(
    project_id: str,
    current_user: User = Depends(get_current_active_user),
    skip: int = Query(0, ge=0),
    limit: int = Query(10, ge=1, le=100),
    sort_on: str = Query("uploaded_at", regex="^(uploaded_at|filename)$"),
    ascending: bool = Query(False),
    all_files: bool = Query(False)
):
    """List all files in a project with their metadata with pagination and sorting.

    Args:
        project_id (str): The ID of the project to list files from
        current_user (User): The authenticated user making the request
        skip (int, optional): Number of records to skip. Defaults to 0.
        limit (int, optional): Maximum number of records to return. Defaults to 10.
        sort_on (str, optional): Field to sort on. Defaults to "uploaded_at".
        ascending (bool, optional): Sort order. Defaults to False (descending).
        all_files (bool, optional): If True, returns all files without pagination. Defaults to False.

    Returns:
        dict: Response containing:
            - total: Total count of files in the project
            - files: Paginated list of files with their metadata (or all files if all_files=True)
            - is_main_document_tag_assigned: Boolean indicating if any file in the project has "Main Document" tag

    Raises:
        HTTPException: 403 for unauthorized access
                      404 if project not found
    """
    if all_files:
        logger.info(f"User {current_user.full_name} requesting ALL files for project {project_id} (no pagination)")
    else:
        logger.info(f"User {current_user.full_name} requesting files for project {project_id} with pagination (skip={skip}, limit={limit})")

    project_manager = await ProjectManager.load_project(project_id)
    if not project_manager:
        raise HTTPException(status_code=404, detail="Project not found")

    if project_manager.project_data.get('user_id') != str(current_user.id):
        raise HTTPException(status_code=403, detail="You don't have permission to access this project")

    result = await project_manager.list_uploaded_files_with_metadata(
        skip=skip,
        limit=limit,
        sort_on=sort_on,
        ascending=ascending,
        all_files=all_files
    )

    await project_manager.update_timestamps(update_content=False, update_visit=True)

    if all_files:
        logger.info(f"Retrieved all {result['total']} files for project {project_id}")
    else:
        logger.info(f"Retrieved {len(result['files'])} files out of {result['total']} total files for project {project_id}")
    return result