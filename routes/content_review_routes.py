"""Content Review Routes Module

This module provides FastAPI routes for content review functionality including:
- Retrieving content review criteria (general and proposal-specific)
- Generating proposal-specific evaluation parameters based on Main Document content
- Managing content review rulebook data
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from typing import Optional, List
from pydantic import BaseModel, Field
import logging
import json
import asyncio

from utils.auth import get_current_active_user, User
from project_manager import ProjectManager
from utils.constants import HTTPStatus, StoragePaths
from content_review import get_content_review_criteria
from utils.llm_config import get_llm_instance
from utils.token_utils import count_tokens
from utils.text_processing import parse_json_proposal_info
from RAG.rfp_prompts import get_review_prompt

router = APIRouter()
logger = logging.getLogger(__name__)


class ContentReviewCriterion(BaseModel):
    """Schema for individual content review criterion.

    This represents a single evaluation rule/criterion that will be used to review content.
    The structure matches what is returned by the GET /projects/{project_id}/content-review-criteria endpoint.
    """
    name: str = Field(..., description="Name of the evaluation criterion", example="Proposal Content Clarity and Conciseness")
    type: str = Field(..., description="Type or category of the criterion", example="General")
    check: str = Field(..., description="Detailed description of what this criterion evaluates and how to assess it", example="Evaluate the proposal's clarity and conciseness by identifying ambiguous or overly verbose sections, scoring its overall readability, and recommending edits to simplify and streamline the language.")


class ContentReviewRequest(BaseModel):
    """Schema for content review request."""
    content_to_be_reviewed: str = Field(..., description="The content text that needs to be reviewed and evaluated")
    criteria: List[ContentReviewCriterion] = Field(..., description="List of evaluation criteria/rules to apply to the content. Each criterion should match the format returned by the GET /projects/{project_id}/content-review-criteria endpoint.")


class ContentReviewResult(BaseModel):
    """Schema for individual content review result."""
    rule_name: str = Field(..., description="Name of the evaluation rule that was applied")
    rule_type: Optional[str] = Field(None, description="Type or category of the evaluation rule (e.g., 'General', 'Factor 1')")
    score: str = Field(..., description="Score assigned by the evaluation (e.g., '8/10' or 'N/A')")
    feedback: str = Field(..., description="Detailed feedback about how the content performs against this rule")
    suggestions: str = Field(..., description="Improvement suggestions or 'N/A' if none")


class ContentReviewResponse(BaseModel):
    """Schema for content review response."""
    content: List[ContentReviewResult] = Field(..., description="List of review results, one for each criterion")
    input_tokens: int = Field(..., description="Total cumulative input tokens used across all evaluations (including previous reviews)")
    output_tokens: int = Field(..., description="Total cumulative output tokens generated across all evaluations (including previous reviews)")


class ContentReviewGetResponse(BaseModel):
    """Schema for GET content review response."""
    content: List[ContentReviewResult] = Field(..., description="List of review results, one for each criterion")
    input_tokens: int = Field(..., description="Total cumulative input tokens used across all evaluations (including previous reviews)")
    output_tokens: int = Field(..., description="Total cumulative output tokens generated across all evaluations (including previous reviews)")


@router.get("/projects/{project_id}/content-review-criteria")
async def get_project_content_review_criteria(
    project_id: str,
    criterias_type: Optional[str] = Query(None, description="Filter by criteria type: 'proposal_criterias' or 'general_criterias'"),
    current_user: User = Depends(get_current_active_user)
):
    """Get content review criteria for a project.

    This endpoint retrieves content review criteria with optional filtering:
    - General criteria are always retrieved from the rulebook
    - Proposal criteria are generated based on the Main Document content or retrieved from cache
    - Supports filtering to return only specific criteria types

    Args:
        project_id (str): The ID of the project
        criterias_type (Optional[str]): Filter by criteria type - "proposal_criterias", "general_criterias", or None for all
        current_user (User): The authenticated user

    Returns:
        dict: Dictionary containing the requested criteria with token metadata
    """
    try:
        # Validate criterias_type parameter
        if criterias_type and criterias_type not in ["proposal_criterias", "general_criterias"]:
            raise HTTPException(
                status_code=HTTPStatus.BAD_REQUEST,
                detail="Invalid criterias_type. Must be 'proposal_criterias' or 'general_criterias'"
            )

        # Load project
        project_manager = await ProjectManager.load_project(project_id)
        if not project_manager:
            raise HTTPException(
                status_code=HTTPStatus.NOT_FOUND,
                detail="Project not found"
            )

        logger.info(f"Getting content review criteria for project {project_id}, type: {criterias_type}")

        # Get criteria based on parameters
        criteria_data = await get_content_review_criteria(
            project_manager=project_manager,
            criterias_type=criterias_type,
            regenerate=False
        )

        # Normalize the response format to ensure consistency
        normalized_data = {}

        # Handle general_criterias - add token info with 0 values
        if "general_criterias" in criteria_data:
            normalized_data["general_criterias"] = {
                "content": criteria_data["general_criterias"],
                "input_tokens": 0,
                "output_tokens": 0
            }

        # Handle proposal_criterias - already has the correct format
        if "proposal_criterias" in criteria_data:
            normalized_data["proposal_criterias"] = criteria_data["proposal_criterias"]

        # Update project visit timestamp
        await project_manager.update_timestamps(update_content=False, update_visit=True)

        logger.info(f"Successfully retrieved content review criteria for project {project_id}")

        return normalized_data

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting content review criteria for project {project_id}: {str(e)}")
        raise HTTPException(
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving content review criteria: {str(e)}"
        )


@router.get("/projects/{project_id}/content-review", response_model=ContentReviewGetResponse)
async def get_content_review(
    project_id: str,
    current_user: User = Depends(get_current_active_user)
) -> ContentReviewGetResponse:
    """Get the content review results for a project.

    This endpoint retrieves the previously generated content review results
    from storage, including all review results and cumulative token counts.

    Args:
        project_id (str): The ID of the project
        current_user (User): The authenticated user

    Returns:
        ContentReviewGetResponse: The content review results with scores, feedback, token usage, and original content
    """
    try:
        # Load project to ensure it exists and user has access
        project_manager = await ProjectManager.load_project(project_id)
        if not project_manager:
            raise HTTPException(
                status_code=HTTPStatus.NOT_FOUND,
                detail="Project not found"
            )

        logger.info(f"Retrieving content review for project {project_id}")

        # Get the content review from storage
        content_review_path = f"{project_manager.project_prefix}{StoragePaths.CONTENT_REVIEW_USER_SPECIFIC_FOLDER}{StoragePaths.GENERATED_CONTENT_REVIEW_FILE}"
        content_review_blob = project_manager.bucket.blob(content_review_path)

        try:
            loop = asyncio.get_event_loop()
            content_review_text = await loop.run_in_executor(None, content_review_blob.download_as_text)
            content_review_data = json.loads(content_review_text)
        except Exception as e:
            logger.error(f"Error loading content review for project {project_id}: {str(e)}")
            raise HTTPException(
                status_code=HTTPStatus.NOT_FOUND,
                detail="Content review not found. Please generate content review first."
            )

        # Remove content_to_be_reviewed from response if it exists (for backward compatibility)
        if "content_to_be_reviewed" in content_review_data:
            del content_review_data["content_to_be_reviewed"]

        # Update project visit timestamp
        await project_manager.update_timestamps(update_content=False, update_visit=True)

        logger.info(f"Retrieved content review for project {project_id}")
        return content_review_data

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving content review for project {project_id}: {str(e)}")
        raise HTTPException(
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving content review: {str(e)}"
        )


@router.post("/projects/{project_id}/review-content", response_model=ContentReviewResponse)
async def review_content(
    project_id: str,
    review_request: ContentReviewRequest,
    current_user: User = Depends(get_current_active_user)
) -> ContentReviewResponse:
    """Review content against provided criteria.

    This endpoint takes content to be reviewed and a list of criteria,
    then returns a structured review with scores and feedback for each criterion.

    Args:
        project_id (str): The ID of the project
        review_request (ContentReviewRequest): The content and criteria for review
        current_user (User): The authenticated user

    Returns:
        ContentReviewResponse: Structured review results with scores, feedback, and token usage
    """
    try:
        # Load project to ensure it exists and user has access
        project_manager = await ProjectManager.load_project(project_id)
        if not project_manager:
            raise HTTPException(
                status_code=HTTPStatus.NOT_FOUND,
                detail="Project not found"
            )

        logger.info(f"Reviewing content for project {project_id}")

        # Load existing content review data to preserve token counts
        content_review_path = f"{project_manager.project_prefix}{StoragePaths.CONTENT_REVIEW_USER_SPECIFIC_FOLDER}{StoragePaths.GENERATED_CONTENT_REVIEW_FILE}"
        content_review_blob = project_manager.bucket.blob(content_review_path)

        # Try to load existing content review data
        prev_input_tokens = 0
        prev_output_tokens = 0
        try:
            existing_review_text = await asyncio.to_thread(content_review_blob.download_as_text)
            existing_review_data = json.loads(existing_review_text)
            prev_input_tokens = existing_review_data.get("input_tokens", 0)
            prev_output_tokens = existing_review_data.get("output_tokens", 0)
            logger.info(f"Loaded existing content review data for token preservation")
        except Exception as e:
            logger.warning(f"Could not load existing content review data: {str(e)}, using defaults")

        logger.info(f"Previous token counts for input and output are: {prev_input_tokens} {prev_output_tokens} respectively.")

        # Get LLM instance for the project
        llm = get_llm_instance(model_name=project_manager.model_name)
        if llm is None:
            raise ValueError("LLM must be provided to review content")

        # Extract content and criteria from request
        context_text = review_request.content_to_be_reviewed
        all_criteria = review_request.criteria

        logger.info(f"Starting content review with {len(all_criteria)} criteria")

        # Initialize review results list
        review_results = []
        total_input_tokens = 0
        total_output_tokens = 0

        # Process each rule/criterion
        for rule in all_criteria:
            try:
                # Convert Pydantic model to dict for get_review_prompt function
                rule_dict = rule.model_dump()

                # Generate prompt for this specific rule
                prompt = get_review_prompt(context_text, rule_dict)

                # Count input tokens
                input_tokens = count_tokens(prompt)
                total_input_tokens += input_tokens

                logger.info(f"Processing rule: {rule.name}")

                # Get LLM response
                loop = asyncio.get_event_loop()

                # Import the required classes for type checking
                from langchain_google_genai import GoogleGenerativeAI

                if isinstance(llm, GoogleGenerativeAI):
                    # For experimental models, invoke and get the response directly
                    response = await loop.run_in_executor(
                        None,
                        lambda: llm.invoke(prompt)
                    )
                    output_tokens = count_tokens(response)
                    response_content = response
                else:
                    # For standard models, invoke and get the structured response
                    response = await loop.run_in_executor(
                        None,
                        lambda: llm.invoke(prompt)
                    )
                    output_tokens = count_tokens(response.content)
                    response_content = response.content

                total_output_tokens += output_tokens

                logger.info(f"Review for Rule: {rule.name}")
                logger.info(f"LLM Response: {response_content}")

                # Extract JSON from response
                try:
                    json_data = parse_json_proposal_info(response_content)
                    review_results.append(json_data)
                    logger.info(f"Successfully parsed JSON for rule '{rule.name}'")

                except json.JSONDecodeError as e:
                    logger.error(f"Error parsing JSON for rule '{rule.name}': {e}")
                    # Skip this result if JSON parsing fails
                    continue

            except Exception as rule_error:
                rule_name = getattr(rule, 'name', 'Unnamed Rule')
                logger.error(f"Error processing rule '{rule_name}': {str(rule_error)}")
                # Skip this result if rule processing fails
                continue

        logger.info(f"Token counts for input and output for the new content review are: {total_input_tokens} {total_output_tokens} respectively.")

        # Prepare the final review data with accumulated token counts
        accumulated_input_tokens = total_input_tokens + prev_input_tokens
        accumulated_output_tokens = total_output_tokens + prev_output_tokens

        # Data to save to GCS (no longer includes the original content)
        review_data_to_save = {
            "content": review_results,
            "input_tokens": accumulated_input_tokens,
            "output_tokens": accumulated_output_tokens
        }

        # Data to return via API
        review_data_to_return = {
            "content": review_results,
            "input_tokens": accumulated_input_tokens,
            "output_tokens": accumulated_output_tokens
        }

        # Save the review results to GCS
        try:
            file_path = f"{project_manager.project_prefix}{StoragePaths.CONTENT_REVIEW_USER_SPECIFIC_FOLDER}{StoragePaths.GENERATED_CONTENT_REVIEW_FILE}"
            blob = project_manager.bucket.blob(file_path)

            await asyncio.to_thread(
                blob.upload_from_string,
                json.dumps(review_data_to_save, indent=2),
                content_type="application/json"
            )

            logger.info(f"Successfully saved content review results to: {file_path}")

        except Exception as save_error:
            logger.error(f"Error saving content review results: {str(save_error)}")
            # Continue execution even if save fails

        # Update project visit timestamp
        await project_manager.update_timestamps(update_content=False, update_visit=True)

        logger.info(f"Successfully reviewed content for project {project_id} with {len(review_results)} results. Total accumulated tokens: input={accumulated_input_tokens}, output={accumulated_output_tokens}")

        return review_data_to_return

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error reviewing content for project {project_id}: {str(e)}")
        raise HTTPException(
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
            detail=f"Error reviewing content: {str(e)}"
        )