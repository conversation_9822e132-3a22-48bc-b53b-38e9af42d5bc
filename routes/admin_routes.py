"""Admin routes module for user management and approval.

This module provides FastAPI routes for administrative functions related to user management,
specifically handling pending user approvals and user status updates. Only administrators
have access to these endpoints.
"""

from fastapi import APIRouter, Depends, HTTPException
from typing import List
import logging
from datetime import datetime
from bson import ObjectId
from utils.auth import get_current_active_user, is_admin, User
from utils.database import users_collection, app_config_collection
from schemas import UserInList, AdminUserUpdate, AIModelCreate, AIModelUpdate, AIModelResponse

# Initialize router and logger
router = APIRouter()
logger = logging.getLogger(__name__)

@router.get("/admin/pending-users", response_model=List[UserInList])
async def get_pending_users(current_user: User = Depends(get_current_active_user)):
    """Retrieve a list of all pending users awaiting approval.

    Args:
        current_user (User): The authenticated user making the request, must be an admin.

    Returns:
        List[UserInList]: A list of pending users who have verified their accounts but haven't been approved.

    Raises:
        HTTPException: 403 error if the current user is not an administrator.
    """
    if not await is_admin(current_user):
        raise HTTPException(
            status_code=403,
            detail="Only administrators can view pending users"
        )

    # Query for verified but unapproved users
    pending_users = await users_collection.find({
        "is_approved": False,
        "is_verified": True
    }).to_list(length=None)

    # Transform database objects into UserInList schema objects
    return [
        UserInList(
            id=str(user["_id"]),
            email=user["email"],
            full_name=user["full_name"],
            phone_number=user["phone_number"],
            is_verified=user["is_verified"],
            is_approved=user.get("is_approved", False),
            created_at=user.get("created_at", datetime.utcnow())
        ) for user in pending_users
    ]

@router.put("/admin/users/{user_id}/approve")
async def approve_user(
    user_id: str,
    update: AdminUserUpdate,
    current_user: User = Depends(get_current_active_user)
):
    """Update the approval status of a specific user.

    Args:
        user_id (str): The ID of the user whose approval status is being updated.
        update (AdminUserUpdate): The update data containing the new approval status.
        current_user (User): The authenticated user making the request, must be an admin.

    Returns:
        dict: A message confirming the successful update.

    Raises:
        HTTPException: 403 error if the current user is not an administrator.
                      404 error if the specified user is not found.
    """
    if not await is_admin(current_user):
        raise HTTPException(
            status_code=403,
            detail="Only administrators can approve users"
        )

    # Update the user's approval status in the database
    result = await users_collection.update_one(
        {"_id": ObjectId(user_id)},
        {"$set": {"is_approved": update.is_approved}}
    )

    if result.modified_count == 0:
        raise HTTPException(status_code=404, detail="User not found")

    return {"message": "User approval status updated successfully"}

# AI Model Management Routes

@router.get("/admin/models", response_model=List[AIModelResponse])
async def get_all_models(current_user: User = Depends(get_current_active_user)):
    """Get all AI models (including inactive ones) for admin management.

    Args:
        current_user (User): The authenticated user making the request, must be an admin.

    Returns:
        List[AIModelResponse]: A list of all AI models with their configuration.

    Raises:
        HTTPException: 403 error if the current user is not an administrator.
    """
    if not await is_admin(current_user):
        raise HTTPException(
            status_code=403,
            detail="Only administrators can manage AI models"
        )

    # Get models configuration from MongoDB
    models_config = await app_config_collection.find_one({"config_type": "ai_models"})

    if not models_config or "models" not in models_config:
        return []

    return [
        AIModelResponse(
            model_name=model["model_name"],
            display_name=model["display_name"],
            description=model["description"],
            is_active=model.get("is_active", True),
            is_default=model.get("is_default", False)
        )
        for model in models_config["models"]
    ]

@router.post("/admin/models", response_model=AIModelResponse)
async def create_model(
    model_data: AIModelCreate,
    current_user: User = Depends(get_current_active_user)
):
    """Create a new AI model configuration.

    Args:
        model_data (AIModelCreate): The model data to create.
        current_user (User): The authenticated user making the request, must be an admin.

    Returns:
        AIModelResponse: The created model configuration.

    Raises:
        HTTPException: 403 error if the current user is not an administrator.
                      400 error if the model already exists.
    """
    if not await is_admin(current_user):
        raise HTTPException(
            status_code=403,
            detail="Only administrators can manage AI models"
        )

    # Get current models configuration
    models_config = await app_config_collection.find_one({"config_type": "ai_models"})

    if not models_config:
        # Create new configuration if it doesn't exist
        models_config = {
            "config_type": "ai_models",
            "models": [],
            "created_at": datetime.now(),
            "updated_at": datetime.now()
        }

    # Check if model already exists
    existing_models = models_config.get("models", [])
    if any(model["model_name"] == model_data.model_name for model in existing_models):
        raise HTTPException(
            status_code=400,
            detail=f"Model '{model_data.model_name}' already exists"
        )

    # If this is set as default, unset other defaults
    if model_data.is_default:
        for model in existing_models:
            model["is_default"] = False

    # Add new model
    new_model = {
        "model_name": model_data.model_name,
        "display_name": model_data.display_name,
        "description": model_data.description,
        "is_active": model_data.is_active,
        "is_default": model_data.is_default
    }

    existing_models.append(new_model)
    models_config["models"] = existing_models
    models_config["updated_at"] = datetime.now()

    # Update or insert the configuration
    await app_config_collection.replace_one(
        {"config_type": "ai_models"},
        models_config,
        upsert=True
    )

    logger.info(f"Admin {current_user.full_name} created new AI model: {model_data.model_name}")

    return AIModelResponse(**new_model)

@router.put("/admin/models/{model_name}", response_model=AIModelResponse)
async def update_model(
    model_name: str,
    model_data: AIModelUpdate,
    current_user: User = Depends(get_current_active_user)
):
    """Update an existing AI model configuration.

    Args:
        model_name (str): The name of the model to update.
        model_data (AIModelUpdate): The updated model data.
        current_user (User): The authenticated user making the request, must be an admin.

    Returns:
        AIModelResponse: The updated model configuration.

    Raises:
        HTTPException: 403 error if the current user is not an administrator.
                      404 error if the model is not found.
    """
    if not await is_admin(current_user):
        raise HTTPException(
            status_code=403,
            detail="Only administrators can manage AI models"
        )

    # Get current models configuration
    models_config = await app_config_collection.find_one({"config_type": "ai_models"})

    if not models_config or "models" not in models_config:
        raise HTTPException(status_code=404, detail="Model not found")

    # Find the model to update
    existing_models = models_config["models"]
    model_index = None
    for i, model in enumerate(existing_models):
        if model["model_name"] == model_name:
            model_index = i
            break

    if model_index is None:
        raise HTTPException(status_code=404, detail="Model not found")

    # Update the model with provided data
    updated_model = existing_models[model_index].copy()

    if model_data.display_name is not None:
        updated_model["display_name"] = model_data.display_name
    if model_data.description is not None:
        updated_model["description"] = model_data.description
    if model_data.is_active is not None:
        updated_model["is_active"] = model_data.is_active
    if model_data.is_default is not None:
        updated_model["is_default"] = model_data.is_default

        # If setting as default, unset other defaults
        if model_data.is_default:
            for i, model in enumerate(existing_models):
                if i != model_index:
                    model["is_default"] = False

    existing_models[model_index] = updated_model
    models_config["models"] = existing_models
    models_config["updated_at"] = datetime.now()

    # Update the configuration
    await app_config_collection.replace_one(
        {"config_type": "ai_models"},
        models_config
    )

    logger.info(f"Admin {current_user.full_name} updated AI model: {model_name}")

    return AIModelResponse(**updated_model)

@router.delete("/admin/models/{model_name}")
async def delete_model(
    model_name: str,
    current_user: User = Depends(get_current_active_user)
):
    """Delete an AI model configuration.

    Args:
        model_name (str): The name of the model to delete.
        current_user (User): The authenticated user making the request, must be an admin.

    Returns:
        dict: A message confirming the successful deletion.

    Raises:
        HTTPException: 403 error if the current user is not an administrator.
                      404 error if the model is not found.
                      400 error if trying to delete the default model.
    """
    if not await is_admin(current_user):
        raise HTTPException(
            status_code=403,
            detail="Only administrators can manage AI models"
        )

    # Get current models configuration
    models_config = await app_config_collection.find_one({"config_type": "ai_models"})

    if not models_config or "models" not in models_config:
        raise HTTPException(status_code=404, detail="Model not found")

    # Find the model to delete
    existing_models = models_config["models"]
    model_to_delete = None
    for model in existing_models:
        if model["model_name"] == model_name:
            model_to_delete = model
            break

    if model_to_delete is None:
        raise HTTPException(status_code=404, detail="Model not found")

    # Prevent deletion of default model
    if model_to_delete.get("is_default", False):
        raise HTTPException(
            status_code=400,
            detail="Cannot delete the default model. Set another model as default first."
        )

    # Remove the model
    existing_models = [model for model in existing_models if model["model_name"] != model_name]
    models_config["models"] = existing_models
    models_config["updated_at"] = datetime.now()

    # Update the configuration
    await app_config_collection.replace_one(
        {"config_type": "ai_models"},
        models_config
    )

    logger.info(f"Admin {current_user.full_name} deleted AI model: {model_name}")

    return {"message": f"Model '{model_name}' deleted successfully"}