"""Outline generation and management routes module.

This module provides FastAPI routes for handling project outlines including:
- Generating new outlines from project context
- Retrieving existing outlines
All endpoints handle proper authentication, logging, and Google Cloud Storage operations.
"""

from fastapi import APIRouter, Depends, HTTPException
import logging
import asyncio
from google.cloud.exceptions import NotFound
import json
from utils.auth import get_current_active_user, User
from project_manager import ProjectManager
from outline_generation import generate_outline
from config import settings
from utils.token_utils import count_tokens
from utils import strip_markdown_delimiters
from utils.constants import HTTPStatus, StoragePaths


router = APIRouter()
logger = logging.getLogger(__name__)

@router.get("/projects/{project_id}/generate-outline")
async def generate_project_outline(
    project_id: str,
    regenerate: bool = False,
    current_user: User = Depends(get_current_active_user)
):
    """Generate or retrieve a project outline based on RFP context.

    Args:
        project_id (str): The ID of the project to generate outline for
        regenerate (bool, optional): Whether to force regeneration of outline. Defaults to False.
        current_user (User): The authenticated user making the request

    Returns:
        dict: The generated or existing outline content

    Raises:
        HTTPException: 400 if RFP context is missing or invalid
                      403 for unauthorized access
                      404 if project not found
                      500 for generation failures
    """
    try:
        project = await ProjectManager.load_project(project_id)
        if not project:
            raise HTTPException(status_code=HTTPStatus.NOT_FOUND, detail="Project not found")

        # Check user permission
        if project.project_data.get('user_id') != str(current_user.id):
            raise HTTPException(status_code=HTTPStatus.FORBIDDEN, detail="You don't have permission to access this project")

        # Check if we need to regenerate the outline
        outline_blob = project.bucket.blob(f"{project.project_prefix}{StoragePaths.OUTLINE_FILE}")
        loop = asyncio.get_event_loop()
        try:
            # Try to load existing outline from storage
            outline_text = await loop.run_in_executor(None, outline_blob.download_as_text)
            outline_data = json.loads(outline_text)
            existing_outline = outline_data.get("content", outline_data.get("outline", ""))  # Support both new and old key for backward compatibility
            prev_input_tokens = outline_data.get("input_tokens", 0)
            prev_output_tokens = outline_data.get("output_tokens", 0)
        except NotFound:
            existing_outline = ""

        logging.info(f"The previous token counts for input and out are: {prev_input_tokens}   {prev_output_tokens} respectively.")
        if regenerate or not existing_outline:
            # Get the RFP context from the project data
            rfp_context = await project.get_outline_generation_context()

            if isinstance(rfp_context, str) and rfp_context.startswith("Error:"):
                # Strip off the "Error:" prefix for a cleaner error message
                error_message = rfp_context[7:].strip()
                raise HTTPException(status_code=HTTPStatus.BAD_REQUEST, detail=error_message)

            if not rfp_context:
                raise HTTPException(
                    status_code=HTTPStatus.BAD_REQUEST,
                    detail="No RFP context found in project. Please ensure all required documents have been uploaded."
                )

            # Generate the outline using the specified model
            outline, input_tokens, output_tokens = await generate_outline(rfp_context, project.model_name)

            if not outline:
                raise HTTPException(status_code=HTTPStatus.INTERNAL_SERVER_ERROR, detail="Failed to generate outline")

            logging.info(f"The token counts for input and output for the new outline are: {input_tokens}   {output_tokens} respectively.")

            # Clean the outline by removing markdown delimiters
            outline = strip_markdown_delimiters(outline)

            # Save the generated outline to storage
            outline_data = {"content": outline,
                            "input_tokens": input_tokens+prev_input_tokens,
                            "output_tokens": output_tokens+prev_output_tokens
                            }

            await project.save_outline(outline_data)

            logger.info(f"Generated and saved outline for project {project_id}")
            return {"content": outline}  # Use standardized "content" key for consistency

        # If not regenerating and outline exists, return the existing outline (cleaned)
        existing_outline = strip_markdown_delimiters(existing_outline)
        logger.info(f"Using existing outline for project {project_id}")
        return {"content": existing_outline}  # Use standardized "content" key for consistency

    except HTTPException as he:
        raise he
    except Exception as e:
        logger.error(f"Error generating outline for project {project_id}: {str(e)}")
        raise HTTPException(status_code=HTTPStatus.INTERNAL_SERVER_ERROR, detail=str(e))

@router.get("/projects/{project_id}/outline")
async def get_project_outline(
    project_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """Retrieve an existing project outline.

    Args:
        project_id (str): The ID of the project to get outline from
        current_user (User): The authenticated user making the request

    Returns:
        dict: The project outline content

    Raises:
        HTTPException: 403 for unauthorized access
                      404 if project or outline not found
                      500 for other errors
    """
    logger.info(f"User {current_user.full_name} requesting outline for project {project_id}")

    try:
        project = await ProjectManager.load_project(project_id)
        if not project:
            raise HTTPException(status_code=HTTPStatus.NOT_FOUND, detail="Project not found")

        # Check if the current user owns the project
        if project.project_data.get('user_id') != str(current_user.id):
            raise HTTPException(status_code=HTTPStatus.FORBIDDEN, detail="You don't have permission to access this project")

        # Get the outline content from storage
        loop = asyncio.get_event_loop()
        outline_blob = project.bucket.blob(f"{project.project_prefix}{StoragePaths.OUTLINE_FILE}")
        try:
            outline_text = await loop.run_in_executor(None, outline_blob.download_as_text)
            outline_data = json.loads(outline_text)
        except NotFound:
            raise HTTPException(
                status_code=HTTPStatus.NOT_FOUND,
                detail="Outline not found. Please generate an outline first."
            )

        # Update project visit timestamp
        await project.update_timestamps(update_content=False, update_visit=True)

        # Clean the outline by removing markdown delimiters
        outline = strip_markdown_delimiters(outline_data.get("content", outline_data.get("outline", "")))  # Support both new and old key for backward compatibility

        logger.info(f"Retrieved outline for project {project_id}")
        return {
            "content": outline  # Use standardized "content" key for consistency
        }

    except HTTPException as he:
        raise he
    except Exception as e:
        logger.error(f"Error retrieving outline for project {project_id}: {str(e)}")
        raise HTTPException(status_code=HTTPStatus.INTERNAL_SERVER_ERROR, detail=str(e))