"""Utility functions for project management.

This module contains utility functions used across the project management system,
including time formatting, section ordering, and other helper functions.
"""

import logging
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any

from utils.constants import Sections

def get_time_ago(uploaded_at: datetime) -> str:
    """Convert a datetime to a human-readable "time ago" string.

    Args:
        uploaded_at: Datetime to convert

    Returns:
        str: Human-readable time difference (e.g., "2 hours ago")
    """
    # Use timezone-aware datetime
    now = datetime.now(timezone.utc)

    # Convert uploaded_at to timezone-aware if it's naive
    if uploaded_at.tzinfo is None:
        uploaded_at = uploaded_at.replace(tzinfo=timezone.utc)

    diff = now - uploaded_at

    seconds = diff.total_seconds()
    if seconds < 60:
        return "just now"

    minutes = seconds / 60
    if minutes < 60:
        minutes = int(minutes)
        if minutes <= 1:
            return "1 minute ago"
        return f"{minutes} minutes ago"

    hours = minutes / 60
    if hours < 24:
        hours = int(hours)
        if hours <= 1:
            return "1 hour ago"
        return f"{hours} hours ago"

    days = diff.days
    if days < 7:
        if days <= 1:
            return "1 day ago"
        return f"{days} days ago"

    weeks = days / 7
    if weeks < 4:
        weeks = int(weeks)
        if weeks <= 1:
            return "1 week ago"
        return f"{weeks} weeks ago"

    months = days / 30.44
    if months < 12:
        months = int(months)
        if months <= 1:
            return "1 month ago"
        return f"{months} months ago"

    years = days / 365.25
    years = int(years)
    if years <= 1:
        return "1 year ago"
    return f"{years} years ago"

def get_section_order(section_title: str) -> int:
    """Get the order index for a section title.

    Args:
        section_title: Title of the section

    Returns:
        int: Order index for sorting
    """
    order = {
        Sections.PROPOSAL_INFO: 0,
        Sections.BACKGROUND: 1,
        Sections.SCOPE: 2,
        Sections.TASK_AREA: 3,
        Sections.SUBMISSION_INSTRUCTIONS: 4,
        Sections.EVALUATION_FACTORS: 5,
        Sections.LIST_OF_ATTACHMENTS: 6,
        Sections.KEY_PERSONNEL: 7,
        "Red Flags": 8  # Custom section not in standard sections
    }
    return order.get(section_title, 999)  # Return high number for unknown sections
