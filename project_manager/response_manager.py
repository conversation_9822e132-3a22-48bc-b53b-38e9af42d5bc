"""Response management module.

This module handles all response-related operations including:
- Response storage and retrieval
- Section management
"""

import asyncio
import json
import logging
from typing import Dict, List, Optional

from google.cloud import storage
from google.cloud.exceptions import NotFound

from project_manager.utils import get_section_order
from utils.constants import StoragePaths


class ResponseManager:
    """Manages responses for projects.

    This class handles all aspects of response management including:
    - Response storage and retrieval
    - Section management

    Attributes:
        project_id: Unique identifier for the project
        project_prefix: Project-specific path prefix in storage
        bucket: GCS bucket for project storage
        responses: Dictionary containing generated responses
    """

    def __init__(self, project_id: str, project_prefix: str, bucket: any, responses: Dict = None):
        """Initialize ResponseManager instance.

        Args:
            project_id: Unique identifier for the project
            project_prefix: Project-specific path prefix in storage
            bucket: GCS bucket for project storage
            responses: Optional dictionary of responses
        """
        self.project_id = project_id
        self.project_prefix = project_prefix
        self.bucket = bucket
        self.responses = responses or {}

    async def add_response(self, section_title: str, response_data: Dict):
        """Add a response for a specific section.

        Args:
            section_title: Title of the section
            response_data: Response data to add
        """
        self.responses[section_title] = response_data
        logging.info(f"Added response data for section '{section_title}'")

    async def get_section_guidelines(self, project_data: Dict, section_title: str) -> str:
        """Get guidelines for a specific section.

        Args:
            project_data: Project configuration data
            section_title: Title of the section

        Returns:
            str: Section guidelines or empty string if not found
        """
        for section in project_data.get('sections', []):
            if section['section_title'] == section_title:
                return section.get('user_guidelines', '')
        logging.warning(f"Section {section_title} not found in project data")
        return ''
