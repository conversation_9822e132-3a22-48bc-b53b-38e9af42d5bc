"""Outline management module.

This module handles all outline-related operations including:
- Outline generation context
- Outline saving and loading
"""

import asyncio
import json
import logging
import pickle
from typing import Dict, List, Optional

from utils.database import projects_collection
from google.cloud.exceptions import NotFound
from utils.constants import StoragePaths, DocumentTags, Sections


class OutlineManager:
    """Manages outlines for projects.

    This class handles all aspects of outline management including:
    - Outline generation context
    - Outline saving and loading

    Attributes:
        project_id: Unique identifier for the project
        project_prefix: Project-specific path prefix in storage
        bucket: GCS bucket for project storage
        outline_data: Dictionary containing outline data
    """

    def __init__(self, project_id: str, project_prefix: str, bucket: any, outline_data: Dict = None):
        """Initialize OutlineManager instance.

        Args:
            project_id: Unique identifier for the project
            project_prefix: Project-specific path prefix in storage
            bucket: GCS bucket for project storage
            outline_data: Optional dictionary of outline data
        """
        self.project_id = project_id
        self.project_prefix = project_prefix
        self.bucket = bucket
        self.outline_data = outline_data or {}

    async def save_outline(self, outline_data: dict):
        """Save outline data to cloud storage.

        Args:
            outline_data: Outline data to save

        Raises:
            Exception: If saving fails
        """
        try:
            outline_path = f"{self.project_prefix}{StoragePaths.OUTLINE_FILE}"
            outline_blob = self.bucket.blob(outline_path)
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(None, outline_blob.upload_from_string,
                                       json.dumps(outline_data, indent=2),
                                       "application/json")

            await projects_collection.update_one(
                {"project_id": self.project_id},
                {
                    "$set": {
                        "outline_path": outline_path
                    }
                }
            )

            logging.info(f"Saved outline for project {self.project_id}")
            self.outline_data = outline_data

        except Exception as e:
            logging.error(f"Error saving outline for project {self.project_id}: {str(e)}")
            raise Exception(f"Failed to save outline: {str(e)}")

    async def get_outline_generation_context(self, get_pickled_content_func) -> str:
        """Get the context needed for outline generation.

        Args:
            get_pickled_content_func: Function to get pickled content for a file

        Returns:
            str: Combined content from all pickled uploaded files or error message
        """
        # List all uploaded files with metadata
        from project_manager.file_manager import FileManager
        file_manager = FileManager(self.project_id, self.project_prefix, self.bucket)
        files_result = await file_manager.list_uploaded_files_with_metadata()
        files_with_metadata = files_result.get('files', [])
        logging.info(f"Found {len(files_with_metadata)} files with metadata for project {self.project_id}")

        if not files_with_metadata:
            return "Error: No uploaded files found in the project. Please upload files first."

        # Check if at least one file has the "Main Document" tag
        has_main_document = False
        for file_info in files_with_metadata:
            if DocumentTags.MAIN_DOCUMENT in file_info.get('tags', []):
                has_main_document = True
                break

        if not has_main_document:
            return f"Error: No file with '{DocumentTags.MAIN_DOCUMENT}' tag found. Please tag at least one file as '{DocumentTags.MAIN_DOCUMENT}'."

        # Collect content from all pickled files
        combined_content = []

        for file_info in files_with_metadata:
            filename = file_info['filename']
            try:
                # Get pickled content for each file
                pickled_data = await get_pickled_content_func(filename)

                if pickled_data:
                    # Unpickle the content
                    sections = pickle.loads(pickled_data)

                    # If sections is a list, join all text content
                    if isinstance(sections, list):
                        for section in sections:
                            if isinstance(section, dict):
                                # Handle different dictionary structures
                                if 'text' in section:
                                    combined_content.append(section['text'])
                                elif 'content' in section:
                                    combined_content.append(section['content'])
                                # Handle PDF page structure
                                elif 'page_number' in section and 'content' in section:
                                    combined_content.append(f"Page {section['page_number']}: {section['content']}")
                            elif isinstance(section, str):
                                combined_content.append(section)
                    # If sections is a string, add it directly
                    elif isinstance(sections, str):
                        combined_content.append(sections)
                    # Handle ProjectDocument object
                    elif hasattr(sections, 'sections') and isinstance(sections.sections, list):
                        for page in sections.sections:
                            if isinstance(page, dict) and 'content' in page:
                                combined_content.append(page['content'])
                    # Log the structure for debugging
                    logging.info(f"Sections type: {type(sections)}")
                    if not combined_content:
                        if isinstance(sections, list):
                            logging.info(f"Sections list length: {len(sections)}")
                            if len(sections) > 0:
                                logging.info(f"First section type: {type(sections[0])}")
                                if isinstance(sections[0], dict):
                                    logging.info(f"First section keys: {sections[0].keys()}")

                    logging.info(f"Added content from pickled file: {filename}")
                else:
                    logging.warning(f"No pickled content found for file: {filename}")
            except Exception as e:
                logging.error(f"Error processing pickled file {filename}: {str(e)}")
                # Continue with other files even if one fails

        # Check if we have any valid content
        valid_content = []
        for content in combined_content:
            # Skip error messages from PDF extraction
            if not content.strip().startswith("Error: No content could be extracted from this page"):
                valid_content.append(content)

        if not valid_content:
            logging.error("No valid content could be extracted from any pickled files")
            # Fall back to using summaries.json if available
            # try:
            #     summaries_blob = self.bucket.blob(f"{self.project_prefix}{StoragePaths.SUMMARIES_FILE}")
            #     loop = asyncio.get_event_loop()
            #     summaries_text = await loop.run_in_executor(None, summaries_blob.download_as_text)
            #     summaries_data = json.loads(summaries_text)

            #     # Try to get content from key sections
            #     fallback_sections = [
            #         Sections.SUBMISSION_INSTRUCTIONS,
            #         Sections.EVALUATION_FACTORS
            #     ]

            #     fallback_content = []
            #     for section_title in fallback_sections:
            #         if section_title in summaries_data:
            #             content = summaries_data[section_title].get("content", "")
            #             if content:
            #                 fallback_content.append(f"## {section_title}\n\n{content}")

            #     if fallback_content:
            #         logging.info("Using fallback content from summaries.json")
            #         combined_text = "\n\n".join(fallback_content)
            #         return combined_text
            # except Exception as e:
            #     logging.error(f"Error getting fallback content: {str(e)}")

            return "Error: Could not extract valid content from any pickled files. Please ensure files are properly uploaded and processed."

        # Log the amount of content extracted
        logging.info(f"Successfully extracted valid content from pickled files: {len(valid_content)} sections, total length: {sum(len(c) for c in valid_content)}")

        # Join all content with newlines
        combined_text = "\n\n".join(valid_content)

        # Log a sample of the content for debugging
        content_sample = combined_text[:500] + "..." if len(combined_text) > 500 else combined_text
        logging.info(f"Content sample: {content_sample}")

        return combined_text
